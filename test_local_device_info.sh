#!/bin/bash

# 测试本机设备信息显示功能
# 验证本机IP是否正确显示在本机信息中

echo "=== 本机设备信息显示测试 ==="
echo ""

# 检查连接的设备
DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -z "$DEVICES" ]; then
    echo "❌ 错误: 没有检测到连接的Android设备"
    exit 1
fi

echo "📱 检测到以下设备:"
for device in $DEVICES; do
    device_model=$(adb -s $device shell getprop ro.product.model 2>/dev/null | tr -d '\r')
    device_ip=$(adb -s $device shell ip route | grep wlan0 | grep -E 'src [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | awk '{print $9}' | head -1 2>/dev/null | tr -d '\r')
    echo "  - $device ($device_model) - IP: $device_ip"
done
echo ""

# 为每台设备测试本机信息显示
for device in $DEVICES; do
    device_model=$(adb -s $device shell getprop ro.product.model 2>/dev/null | tr -d '\r')
    device_ip=$(adb -s $device shell ip route | grep wlan0 | grep -E 'src [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | awk '{print $9}' | head -1 2>/dev/null | tr -d '\r')
    
    echo "🧪 测试设备: $device ($device_model) - IP: $device_ip"
    echo ""
    
    # 启动应用
    echo "  📱 启动应用..."
    adb -s $device shell am start -n com.haoxueren.pray/.main.MainActivity
    sleep 2
    
    # 进入数据库同步界面
    echo "  🔄 进入数据库同步界面..."
    adb -s $device shell am start -n com.haoxueren.pray/.sync.DatabaseSyncActivity
    sleep 3
    
    echo "  📋 检查本机信息显示..."
    
    # 检查应用日志中的本机IP信息
    LOG_OUTPUT=$(adb -s $device logcat -d -s DeviceDiscovery:* | tail -10)
    
    if echo "$LOG_OUTPUT" | grep -q "本机IP"; then
        echo "  ✅ 在日志中找到本机IP信息"
    else
        echo "  ⚠️  在日志中未找到本机IP信息"
    fi
    
    echo ""
    echo "  💡 手动验证步骤:"
    echo "    1. 在设备上查看 '数据库同步' 界面"
    echo "    2. 检查 '本机信息' 卡片是否显示:"
    echo "       - 设备型号: $device_model (本机)"
    echo "       - 本机IP: $device_ip"
    echo "       - 数据库大小、记录数等信息"
    echo "    3. 点击本机信息卡片，查看详细信息对话框"
    echo "    4. 检查详细信息是否包含:"
    echo "       - 设备型号、制造商、Android版本"
    echo "       - 本机IP地址"
    echo "       - WiFi网络信息"
    echo "       - 服务端口状态"
    echo "    5. 测试 '复制IP' 按钮功能"
    echo ""
    
    read -p "  ❓ 请在设备上手动验证，本机信息是否正确显示IP地址? (y/n): " ip_display_result
    
    if [ "$ip_display_result" = "y" ] || [ "$ip_display_result" = "Y" ]; then
        echo "  ✅ 本机IP显示验证通过"
    else
        echo "  ❌ 本机IP显示验证失败"
        echo "  🔧 建议检查:"
        echo "    - 确保设备已连接WiFi网络"
        echo "    - 确保应用有网络权限"
        echo "    - 查看应用日志: adb -s $device logcat | grep DeviceDiscovery"
    fi
    
    read -p "  ❓ 点击本机信息卡片，详细信息对话框是否正常显示? (y/n): " detail_dialog_result
    
    if [ "$detail_dialog_result" = "y" ] || [ "$detail_dialog_result" = "Y" ]; then
        echo "  ✅ 详细信息对话框验证通过"
    else
        echo "  ❌ 详细信息对话框验证失败"
    fi
    
    read -p "  ❓ '复制IP' 按钮是否能正常复制IP地址? (y/n): " copy_ip_result
    
    if [ "$copy_ip_result" = "y" ] || [ "$copy_ip_result" = "Y" ]; then
        echo "  ✅ 复制IP功能验证通过"
    else
        echo "  ❌ 复制IP功能验证失败"
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
done

echo "=== 测试完成 ==="
echo ""
echo "📋 功能总结:"
echo "✅ 本机信息卡片显示本机IP地址"
echo "✅ 点击本机信息查看详细设备信息"
echo "✅ 复制IP地址到剪贴板功能"
echo "✅ 改进的UI设计，添加可点击提示图标"
echo ""
echo "🎯 主要改进:"
echo "- 本机信息中新增本机IP显示"
echo "- 可点击查看详细设备信息"
echo "- 包含设备型号、制造商、Android版本"
echo "- 显示网络信息和服务状态"
echo "- 一键复制IP地址功能"
echo ""
echo "📖 更多信息请参考: 网络发现问题解决方案.md"
