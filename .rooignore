# Roo Ignore Configuration
# 该文件定义了Roo在项目中应该忽略的文件和目录
# 支持glob模式和注释

# =====================
# 操作系统特定文件
# =====================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
Icon?
.AppleDouble
.LSOverride

# Linux
*~
.directory
.Trash-*

# =====================
# IDE和编辑器文件
# =====================
# IntelliJ IDEA / Android Studio
.idea/
*.iml
*.iws
*.ipr
.idea_modules/
atlassian-ide-plugin.xml

# VS Code
.vscode/
*.code-workspace
.history/
*.vsix

# Eclipse
.classpath
.project
.settings/
.metadata/
bin/
tmp/

# NetBeans
nbproject/
nbbuild/
dist/
nbdist/
.nb-gradle/

# Vim
*.swp
*.swo
*~
.tags
.tags_sorted_by_file

# Emacs
*~
\#*\#
.\#*

# Sublime Text
*.sublime-project
*.sublime-workspace

# =====================
# 构建系统输出
# =====================
# Gradle
.gradle/
build/
gradle-app.setting
!gradle-wrapper.jar
!gradle-wrapper.properties

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# Ant
build.xml
ant.properties

# =====================
# 语言和运行时
# =====================
# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
.replay_pid*

# Kotlin
*.kotlin_module
*.kotlin_builtins

# Android
*.apk
*.ap_
*.aab
*.dex
local.properties
proguard/
lint-results*.xml
lint-report*.html

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-store/
.nyc_output/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/
.tox/
*.egg-info/
dist/
build/
eggs/
parts/
var/
sdist/
develop-eggs/
.installed.cfg
lib/
lib64/

# Ruby
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
go.work.sum

# C/C++
*.o
*.a
*.so
*.dylib
*.exe
*.out
*.app

# Rust
target/
Cargo.lock
**/*.rs.bk

# =====================
# 测试和代码质量
# =====================
# 测试报告
test-results/
coverage/
.nyc_output/
coverage.xml
*.cover
.hypothesis/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 代码质量工具
.sonar/
.sonarlint/
.sonarqube/
checkstyle-cachefile
checkstyle-result.xml
detekt-report.xml
ktlint-report.xml

# =====================
# 依赖和包管理
# =====================
# 依赖锁文件（根据项目需要选择性忽略）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# =====================
# 日志和临时文件
# =====================
*.log
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig
*.rej
*.swp
*.swo
*~
.DS_Store?
.Trashes
._*

# =====================
# 环境配置
# =====================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 敏感配置
config.properties
secrets.yaml
keystore.properties
google-services.json
*.jks
*.keystore

# =====================
# 数据库
# =====================
*.db
*.sqlite
*.sqlite3
*.db-journal
*.sqlitedb
*.sql
*.sqlite-shm
*.sqlite-wal

# =====================
# 媒体文件
# =====================
# 大文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z
*.iso
*.dmg

# 媒体缓存
*.cache
*.tmp

# =====================
# 文档和笔记
# =====================
~$*
*.tmp.docx
*.tmp.xlsx
*.tmp.pptx

# =====================
# 项目特定
# =====================
# Android特定
app/release/
app/debug/
*.aab
*.apk
lint-results*.xml
lint-report*.html

# 本地开发文件
local.properties
gradle.properties.local