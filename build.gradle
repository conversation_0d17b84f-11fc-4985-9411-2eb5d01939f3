// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    
    ext {
        kotlin_version = '2.1.21'
    }
    repositories {
        maven{ url 'https://maven.aliyun.com/nexus/content/groups/public/'}
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // classpath "org.jetbrains.kotlin:kotlin-compose-compiler-plugin-gradle:$kotlin_version" // Removed, let KGP handle it
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

// allprojects block removed as repositories are now managed in settings.gradle
// allprojects {
//     repositories {
//         maven{ url 'https://maven.aliyun.com/nexus/content/groups/public/'}
//         google()
//         mavenCentral()
//     }
// }

task clean(type: Delete) {
    delete rootProject.buildDir
}
