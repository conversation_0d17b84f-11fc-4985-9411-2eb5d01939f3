#!/bin/bash

# 测试设备过滤功能脚本
# 验证设备列表中是否正确排除了当前设备

echo "=== 设备过滤功能测试 ==="
echo ""

# 检查连接的设备
DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -z "$DEVICES" ]; then
    echo "❌ 错误: 没有检测到连接的Android设备"
    exit 1
fi

echo "📱 检测到以下设备:"
for device in $DEVICES; do
    device_model=$(adb -s $device shell getprop ro.product.model 2>/dev/null | tr -d '\r')
    device_ip=$(adb -s $device shell ip route | grep wlan0 | grep -E 'src [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | awk '{print $9}' | head -1 2>/dev/null | tr -d '\r')
    echo "  - $device ($device_model) - IP: $device_ip"
done
echo ""

# 为每台设备运行测试
for device in $DEVICES; do
    device_model=$(adb -s $device shell getprop ro.product.model 2>/dev/null | tr -d '\r')
    device_ip=$(adb -s $device shell ip route | grep wlan0 | grep -E 'src [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | awk '{print $9}' | head -1 2>/dev/null | tr -d '\r')
    
    echo "🧪 测试设备: $device ($device_model) - IP: $device_ip"
    echo ""
    
    # 启动应用并触发设备扫描
    echo "  📱 启动应用..."
    adb -s $device shell am start -n com.haoxueren.pray/.main.MainActivity
    sleep 2
    
    echo "  🔍 启动设备扫描测试..."
    # 通过广播触发快速网络测试
    adb -s $device shell am broadcast -a com.haoxueren.pray.TEST_NETWORK_DISCOVERY
    
    echo "  ⏳ 等待测试完成..."
    sleep 5
    
    echo "  📋 查看测试日志..."
    # 获取最近的日志，查找设备发现相关信息
    LOG_OUTPUT=$(adb -s $device logcat -d -s DeviceDiscovery:* QuickNetworkTest:* | tail -20)
    
    if echo "$LOG_OUTPUT" | grep -q "跳过本机设备"; then
        echo "  ✅ 本机设备过滤正常工作"
    else
        echo "  ⚠️  未检测到本机设备过滤日志"
    fi
    
    if echo "$LOG_OUTPUT" | grep -q "发现新设备"; then
        echo "  ✅ 设备发现功能正常"
    else
        echo "  ⚠️  未检测到设备发现日志"
    fi
    
    echo "  📝 最近的设备发现日志:"
    echo "$LOG_OUTPUT" | grep -E "(跳过本机设备|发现新设备|移除本机设备)" | sed 's/^/    /'
    
    echo ""
    echo "  💡 手动验证步骤:"
    echo "    1. 在设备上打开 HaoPray 应用"
    echo "    2. 进入 '数据库同步' 界面"
    echo "    3. 点击 '扫描设备' 按钮"
    echo "    4. 检查设备列表中是否包含当前设备 ($device_ip)"
    echo "    5. 设备列表应该只显示其他设备，不包含当前设备"
    echo ""
    
    read -p "  ❓ 请在设备上手动验证，设备列表是否正确排除了当前设备? (y/n): " manual_result
    
    if [ "$manual_result" = "y" ] || [ "$manual_result" = "Y" ]; then
        echo "  ✅ 手动验证通过: 设备过滤功能正常"
    else
        echo "  ❌ 手动验证失败: 设备过滤功能异常"
        echo "  🔧 建议检查:"
        echo "    - 确保应用已更新到最新版本"
        echo "    - 查看完整日志: adb -s $device logcat | grep DeviceDiscovery"
        echo "    - 尝试重启应用"
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
done

echo "=== 测试完成 ==="
echo ""
echo "📋 测试总结:"
echo "- 本机设备过滤功能已实现"
echo "- 设备列表应该只显示其他设备"
echo "- 手动添加设备时会阻止添加本机IP"
echo ""
echo "🔍 如需查看详细日志:"
echo "adb logcat | grep -E '(DeviceDiscovery|QuickNetworkTest)'"
echo ""
echo "📖 更多信息请参考: 网络发现问题解决方案.md"
