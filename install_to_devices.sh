#!/bin/bash

# 安装脚本 - 将修复后的APK安装到两台设备
# 使用方法: ./install_to_devices.sh

echo "=== HaoPray 网络发现修复版本安装脚本 ==="
echo ""

# APK文件路径
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"

# 检查APK文件是否存在
if [ ! -f "$APK_PATH" ]; then
    echo "❌ 错误: APK文件不存在: $APK_PATH"
    echo "请先运行: ./gradlew assembleDebug"
    exit 1
fi

echo "✅ 找到APK文件: $APK_PATH"
echo "📱 APK大小: $(ls -lh $APK_PATH | awk '{print $5}')"
echo ""

# 检查连接的设备
echo "🔍 检查连接的Android设备..."
DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')

if [ -z "$DEVICES" ]; then
    echo "❌ 错误: 没有检测到连接的Android设备"
    echo "请确保:"
    echo "  1. 设备已通过USB连接到电脑"
    echo "  2. 设备已开启USB调试"
    echo "  3. 已授权电脑进行调试"
    exit 1
fi

echo "📱 检测到以下设备:"
for device in $DEVICES; do
    device_model=$(adb -s $device shell getprop ro.product.model 2>/dev/null | tr -d '\r')
    device_ip=$(adb -s $device shell ip route | grep wlan0 | grep -E 'src [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | awk '{print $9}' | head -1 2>/dev/null | tr -d '\r')
    echo "  - $device ($device_model) - IP: $device_ip"
done
echo ""

# 安装到所有设备
echo "🚀 开始安装到所有设备..."
echo ""

install_count=0
for device in $DEVICES; do
    device_model=$(adb -s $device shell getprop ro.product.model 2>/dev/null | tr -d '\r')
    device_ip=$(adb -s $device shell ip route | grep wlan0 | grep -E 'src [0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | awk '{print $9}' | head -1 2>/dev/null | tr -d '\r')
    
    echo "📱 安装到设备: $device ($device_model) - IP: $device_ip"
    
    # 卸载旧版本（如果存在）
    echo "  🗑️  卸载旧版本..."
    adb -s $device uninstall com.haoxueren.pray 2>/dev/null
    
    # 安装新版本
    echo "  📦 安装新版本..."
    if adb -s $device install "$APK_PATH"; then
        echo "  ✅ 安装成功!"
        install_count=$((install_count + 1))
        
        # 启动应用
        echo "  🚀 启动应用..."
        adb -s $device shell am start -n com.haoxueren.pray/.main.MainActivity
        
        echo "  📋 设备信息:"
        echo "    - 设备ID: $device"
        echo "    - 设备型号: $device_model"
        echo "    - IP地址: $device_ip"
        echo "    - 安装状态: ✅ 成功"
    else
        echo "  ❌ 安装失败!"
    fi
    echo ""
done

echo "=== 安装完成 ==="
echo "📊 安装统计: $install_count 台设备安装成功"
echo ""

if [ $install_count -gt 0 ]; then
    echo "🎉 安装成功! 接下来请按照以下步骤测试网络发现功能:"
    echo ""
    echo "📋 测试步骤:"
    echo "1. 在两台设备上打开 HaoPray 应用"
    echo "2. 进入 '数据库同步' 界面"
    echo "3. 点击右上角菜单 → '网络诊断'"
    echo "4. 在设备B上输入设备A的IP地址 (***********)"
    echo "5. 在设备A上输入设备B的IP地址 (************)"
    echo "6. 点击 '开始诊断' 查看详细报告"
    echo "7. 如有问题，点击 '修复问题' 按钮"
    echo ""
    echo "🔧 快速测试:"
    echo "- 在同步界面点击菜单 → '测试功能' → '快速网络测试'"
    echo "- 查看 Logcat 日志: adb logcat | grep -E '(DeviceDiscovery|QuickNetworkTest)'"
    echo ""
    echo "📝 主要修复内容:"
    echo "- ✅ 增强的UDP响应服务器，支持端口重试"
    echo "- ✅ 改进的UDP发现机制，多次广播提高成功率"
    echo "- ✅ 应用启动时自动启动网络服务"
    echo "- ✅ 专业的网络诊断和测试工具"
    echo "- ✅ 详细的日志记录便于问题排查"
else
    echo "❌ 没有设备安装成功，请检查设备连接和权限设置"
fi

echo ""
echo "📞 如需帮助，请查看 '网络发现问题解决方案.md' 文档"
echo "🔗 或查看项目日志进行问题排查"
