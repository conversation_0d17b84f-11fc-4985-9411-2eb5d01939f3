pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://maven.pkg.jetbrains.space/public/p/compose/dev" }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://maven.pkg.jetbrains.space/public/p/compose/dev" }
        // You can add other repositories like jcenter() or JitPack if needed
        // maven { url 'https://jitpack.io' }
    }
}

rootProject.name = "HaoPray"
include ':app'
