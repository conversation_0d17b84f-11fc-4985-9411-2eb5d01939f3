<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/margin_default"
    android:layout_marginRight="@dimen/margin_default"
    android:background="@drawable/shape_underline"
    android:orientation="horizontal"
    android:longClickable="true"
    android:focusable="true"
    tools:ignore="RtlHardcoded">

    <TextView
        android:id="@+id/groupIdView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:paddingTop="@dimen/padding_default"
        android:paddingRight="4dp"
        android:paddingBottom="@dimen/padding_default"
        android:textColor="@color/tc_primary" />

    <com.haoxueren.view.HorizontalScrollTextView
        android:id="@+id/prayTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/padding_default"
        android:paddingBottom="@dimen/padding_default"
        android:textColor="@color/tc_primary"
        android:textSize="@dimen/textSize_normal"
        android:longClickable="true"
        android:focusable="false"
        android:focusableInTouchMode="false" />

</LinearLayout>
