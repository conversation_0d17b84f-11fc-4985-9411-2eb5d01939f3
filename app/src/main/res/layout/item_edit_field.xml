<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="16dp">

    <!-- 字段标签 -->
    <TextView
        android:id="@+id/fieldLabelTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="字段名"
        android:textSize="14sp"
        android:textColor="@color/tc_primary"
        android:layout_marginBottom="4dp" />

    <!-- 字段输入框 -->
    <EditText
        android:id="@+id/fieldValueEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_input_bg"
        android:padding="12dp"
        android:textSize="14sp"
        android:textColor="@color/tc_primary"
        android:textColorHint="@color/tc_secondary"
        android:hint="请输入值"
        android:maxLines="5"
        android:scrollbars="vertical" />

</LinearLayout>
