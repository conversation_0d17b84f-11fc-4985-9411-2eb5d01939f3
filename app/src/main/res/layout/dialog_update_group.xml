<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/shape_bottom_sheet_bg"
    android:paddingBottom="@dimen/padding_default"
    tools:ignore="HardcodedText">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_title_bg"
        android:padding="@dimen/padding_default"
        android:paddingTop="20dp"
        android:text="修改分组"
        android:textColor="@color/bg_primary"
        android:textSize="@dimen/textSize_big"
        android:textStyle="bold"
        android:gravity="center" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_default"
        android:layout_marginRight="@dimen/padding_default"
        android:layout_marginTop="@dimen/padding_default"
        android:padding="@dimen/padding_half"
        android:text="分组ID (GroupId)"
        android:textColor="@color/tc_secondary"
        android:textSize="@dimen/textSize_normal" />

    <EditText
        android:id="@+id/groupIdEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_default"
        android:layout_marginRight="@dimen/padding_default"
        android:inputType="numberSigned"
        android:padding="@dimen/padding_default"
        android:selectAllOnFocus="true"
        android:textSize="@dimen/textSize_normal"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_default"
        android:layout_marginRight="@dimen/padding_default"
        android:padding="@dimen/padding_half"
        android:text="祈祷ID (PrayId)"
        android:textColor="@color/tc_secondary"
        android:textSize="@dimen/textSize_normal" />

    <EditText
        android:id="@+id/prayIdEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_default"
        android:layout_marginRight="@dimen/padding_default"
        android:padding="@dimen/padding_default"
        android:selectAllOnFocus="true"
        android:textSize="@dimen/textSize_normal"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_default"
        android:layout_marginRight="@dimen/padding_default"
        android:padding="@dimen/padding_half"
        android:text="祈祷内容 (Pray)"
        android:textColor="@color/tc_secondary"
        android:textSize="@dimen/textSize_normal" />

    <EditText
        android:id="@+id/groupTextEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_default"
        android:layout_marginRight="@dimen/padding_default"
        android:padding="@dimen/padding_default"
        android:selectAllOnFocus="true"
        android:textSize="@dimen/textSize_normal"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_margin="@dimen/padding_default"
        android:layout_marginTop="20dp">

        <Button
            android:id="@+id/detailButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="@dimen/padding_half"
            android:text="详情"
            android:textSize="@dimen/textSize_normal"
            android:textStyle="bold" />

        <Button
            android:id="@+id/updateButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/padding_half"
            android:text="更新"
            android:textSize="@dimen/textSize_normal"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>