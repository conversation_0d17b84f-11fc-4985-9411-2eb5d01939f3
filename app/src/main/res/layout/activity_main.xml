<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_primary"
    tools:ignore="RtlHardcoded,HardcodedText">

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/toolbarComposeView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/prayLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_default"
        android:background="@drawable/shape_bg_border"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/toolbarComposeView">

        <EditText
            android:id="@+id/dateEditText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:focusable="false"
            android:paddingLeft="@dimen/padding_default"
            android:paddingTop="@dimen/padding_default"
            android:paddingRight="0dp"
            android:paddingBottom="@dimen/padding_default"
            android:textColor="@color/accent"
            android:textSize="@dimen/textSize_normal"
            android:textStyle="bold"
            tools:text="2019.06.23" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="."
            android:textColor="@color/accent"
            android:textSize="@dimen/textSize_normal"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/countEditText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:focusable="false"
            android:paddingLeft="0dp"
            android:paddingTop="@dimen/padding_default"
            android:paddingRight="@dimen/padding_half"
            android:paddingBottom="@dimen/padding_default"
            android:text="0"
            android:textColor="@color/accent"
            android:textSize="@dimen/textSize_normal"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/prayEditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:paddingTop="@dimen/padding_default"
            android:paddingBottom="@dimen/padding_default"
            android:singleLine="true"
            android:textColor="@color/accent"
            android:textSize="@dimen/textSize_normal"
            android:textStyle="bold" />

    </LinearLayout>

    <com.haoxueren.view.StatefulLayout
        android:id="@+id/stateLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/margin_default"
        android:background="@drawable/shape_bg_border"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/prayLayout">

        <com.haoxueren.widget.PrayRecordWidget
            android:id="@+id/recordWidget"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.haoxueren.view.StatefulLayout>

</androidx.constraintlayout.widget.ConstraintLayout>