<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_normal_item"
    android:clickable="true"
    android:focusable="true"
    android:orientation="horizontal"
    android:padding="16dp">

    <ImageView
        android:id="@+id/deviceIcon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_device_online"
        android:contentDescription="设备图标" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/deviceName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="小米手机 (本机)" />

        <TextView
            android:id="@+id/deviceAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            tools:text="*************:1986" />

        <TextView
            android:id="@+id/deviceLastSeen"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            tools:text="最后见到: 12-25 14:30" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="vertical"
        android:gravity="end">

        <TextView
            android:id="@+id/deviceStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_status_badge"
            android:paddingStart="8dp"
            android:paddingTop="4dp"
            android:paddingEnd="8dp"
            android:paddingBottom="4dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            tools:text="在线" />

        <View
            android:id="@+id/selectionIndicator"
            android:layout_width="20dp"
            android:layout_height="4dp"
            android:layout_marginTop="8dp"
            android:background="@color/theme"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</LinearLayout>
