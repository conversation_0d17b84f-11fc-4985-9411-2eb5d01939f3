<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/theme"
    tools:ignore="HardcodedText">

    <TextView
        android:id="@+id/failureTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:drawableTop="@drawable/ic_sync_problem_48dp"
        android:drawablePadding="5dp"
        android:gravity="center"
        android:padding="16dp"
        android:text="加载失败"
        android:textColor="@color/bg_primary" />
</FrameLayout>
