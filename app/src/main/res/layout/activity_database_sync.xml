<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.haoxueren.pray.sync.DatabaseSyncActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/theme"
            app:navigationIcon="@drawable/ic_back_24x24"
            app:title="数据库同步"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 本地设备信息 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/localInfoLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="本机信息"
                            android:textColor="@color/theme"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/localInfoTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="正在加载本机信息..."
                            android:textSize="14sp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="8dp"
                        android:src="@drawable/ic_info_24x24"
                        android:contentDescription="查看详细信息" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 设备扫描控制 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/scanButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="扫描设备"
                    android:textAllCaps="false" />

                <Button
                    android:id="@+id/addDeviceButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="手动添加"
                    android:textAllCaps="false"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- 设备列表 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="16dp"
                        android:text="可用设备"
                        android:textColor="@color/theme"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/deviceRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:maxHeight="300dp"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_device_sync" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:padding="16dp"
                        android:text="暂无可用设备\n点击扫描设备或手动添加来添加设备"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 同步控制 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="同步操作"
                        android:textColor="@color/theme"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <ProgressBar
                        android:id="@+id/progressBar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:visibility="gone"
                        tools:progress="50"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/statusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="请选择要同步的设备"
                        android:textSize="14sp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/syncButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:enabled="false"
                        android:text="开始同步"
                        android:textAllCaps="false"
                        app:backgroundTint="@color/button_sync_background" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="⚠️ 同步将完全覆盖本地数据库，请确保已备份重要数据"
                        android:textColor="@color/warning"
                        android:textSize="12sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/refreshFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_refresh"
        app:backgroundTint="@color/theme"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
