<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground"
    android:minHeight="48dp"
    android:gravity="center_vertical"
    tools:ignore="RtlHardcoded">

    <TextView
        android:id="@+id/tableNameTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/tc_primary"
        android:textSize="@dimen/textSize_normal"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="HaoPray" />

    <TextView
        android:id="@+id/recordCountTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/tc_secondary"
        android:textSize="11sp"
        tools:text="123条记录" />

</LinearLayout>
