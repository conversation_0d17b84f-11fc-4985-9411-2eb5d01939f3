<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="HardcodedText">

    <com.haoxueren.view.TitleEditText
        android:id="@+id/fromIdView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_underline"
        android:hint="请输入目标id"
        android:paddingTop="@dimen/padding_default"
        android:paddingBottom="@dimen/padding_default"
        android:title="测试库：" />

    <com.haoxueren.view.TitleEditText
        android:id="@+id/newIdView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入目标id"
        android:inputType="number"
        android:paddingTop="@dimen/padding_default"
        android:paddingBottom="@dimen/padding_default"
        android:title="正式库：" />

    <Button
        android:id="@+id/migrateButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开始迁移" />

    <TextView
        android:id="@+id/messageView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="10dp" />

</LinearLayout>