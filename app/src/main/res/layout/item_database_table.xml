<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:orientation="vertical"
    android:padding="@dimen/padding_default"
    tools:ignore="RtlHardcoded">

    <TextView
        android:id="@+id/tableNameTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/tc_primary"
        android:textSize="@dimen/textSize_normal"
        android:textStyle="bold"
        tools:text="HaoPray" />

    <TextView
        android:id="@+id/recordCountTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/tc_secondary"
        android:textSize="12sp"
        tools:text="记录数: 123" />

</LinearLayout>
