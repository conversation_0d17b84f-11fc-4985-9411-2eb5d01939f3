<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".group.AllGroupActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/theme"
        android:theme="@style/ToolbarTheme"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:navigationIcon="@drawable/ic_back_24x24"
        app:subtitleTextColor="@color/white" />

    <TextView
        android:id="@+id/prayIdView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_default"
        android:layout_marginRight="@dimen/padding_default"
        android:background="@drawable/shape_underline"
        android:paddingTop="@dimen/padding_half"
        android:paddingBottom="@dimen/padding_half" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/margin_default">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>


</LinearLayout>