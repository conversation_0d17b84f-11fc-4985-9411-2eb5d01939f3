<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/padding_default">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="📊"
        android:textSize="48sp"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/emptyMessageTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请选择要查看的表"
        android:textColor="@color/tc_secondary"
        android:textSize="16sp"
        android:gravity="center" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="在左侧选择数据库表，然后可以搜索或浏览数据"
        android:textColor="@color/tc_secondary"
        android:textSize="12sp"
        android:gravity="center" />

</LinearLayout>
