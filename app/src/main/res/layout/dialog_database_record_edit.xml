<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/shape_bottom_sheet_bg"
    android:padding="24dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"
        android:background="@color/tc_secondary"
        android:alpha="0.5" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="编辑记录"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/tc_primary"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 滚动容器，用于容纳动态生成的字段 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="500dp">

        <LinearLayout
            android:id="@+id/fieldsContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

    </ScrollView>

    <!-- 错误提示 -->
    <TextView
        android:id="@+id/errorTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/error"
        android:textSize="14sp"
        android:layout_marginTop="8dp"
        android:visibility="gone" />

    <!-- 按钮容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="24dp">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:textColor="@color/tc_primary"
            android:background="@drawable/shape_button_outline"
            android:layout_marginEnd="8dp"
            style="?android:attr/borderlessButtonStyle" />

        <Button
            android:id="@+id/saveButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存"
            android:textColor="@android:color/white"
            android:background="@drawable/shape_button_primary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
