<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/theme"
    tools:ignore="HardcodedText">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@color/theme"
        android:drawableTop="@drawable/vector_ic_receipt_48dp"
        android:drawablePadding="5dp"
        android:text="暂无数据"
        android:textColor="@color/bg_primary" />
</FrameLayout>