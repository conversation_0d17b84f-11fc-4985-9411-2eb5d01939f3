<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/theme</item>
        <item name="colorPrimaryDark">@color/theme_dark</item>
        <item name="colorAccent">@color/accent</item>
    </style>

    <style name="ToolbarTheme">
        <item name="overlapAnchor">false</item>
        <item name="contentInsetStart">0dp</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="contentInsetStartWithNavigation">0dp</item>
    </style>

    <declare-styleable name="SuperRecyclerView">
        <attr name="emptyLayout" format="reference" />
    </declare-styleable>

    <!-- 底部弹窗样式 -->
    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>

    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/shape_bottom_sheet_bg</item>
        <item name="android:elevation">8dp</item>
    </style>

    <declare-styleable name="TitleEditText">
        <attr name="android:title" />
        <attr name="android:hint" />
        <attr name="android:text" />
        <attr name="android:gravity" />
        <attr name="android:enabled" />
        <attr name="android:inputType" />
        <attr name="android:orientation" />
    </declare-styleable>
</resources>
