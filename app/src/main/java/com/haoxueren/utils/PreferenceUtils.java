package com.haoxueren.utils;

import android.content.Context;

import androidx.preference.PreferenceManager;

import java.util.Set;

public class PreferenceUtils {

    public static void putStringSet(String key, Set<String> value) {
        Context context = ContextManager.getContext();
        PreferenceManager.getDefaultSharedPreferences(context)
                .edit().putStringSet(key, value).apply();
    }

    public static Set<String> getStringSet(String key) {
        Context context = ContextManager.getContext();
        return PreferenceManager.getDefaultSharedPreferences(context)
                .getStringSet(key, null);
    }

    public static void putString(String key, String value) {
        Context context = ContextManager.getContext();
        PreferenceManager.getDefaultSharedPreferences(context)
                .edit().putString(key, value).apply();
    }

    public static String getString(String key) {
        Context context = ContextManager.getContext();
        return PreferenceManager.getDefaultSharedPreferences(context)
                .getString(key, "");
    }
}
