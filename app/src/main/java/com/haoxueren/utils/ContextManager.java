package com.haoxueren.utils;

import android.content.Context;

/**
 * 获取上下文对象的帮助类；<br>
 */
public class ContextManager {

    private static Context context;

    /**
     * 通过调用者传入上下文对象；
     */
    public static void initContext(Context context) {
        // 通过用户设置的context获取ApplicationContext，避免内在泄漏；
        ContextManager.context = context.getApplicationContext();
    }

    /**
     * 获取ApplicationContext对象；
     */
    public static Context getContext() {
        return context;
    }


}
