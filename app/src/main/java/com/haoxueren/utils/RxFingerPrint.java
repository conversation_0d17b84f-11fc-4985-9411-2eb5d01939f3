package com.haoxueren.utils;

import android.app.Activity;
import android.hardware.biometrics.BiometricPrompt;
import android.os.Build;

import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import androidx.core.os.CancellationSignal;

import java.util.concurrent.Executor;

import io.reactivex.Observable;

public class RxFingerPrint {

    private CancellationSignal cancellationSignal = new CancellationSignal();

    /**
     * 取消指纹验证
     */
    public void cancelAuthentication() {
        cancellationSignal.cancel();
    }

    /**
     * 启动指纹验证
     */
    public Observable<Boolean> authenticate(Activity activity) {
        return Observable.create(emitter -> {
            FingerprintManagerCompat manager = FingerprintManagerCompat.from(activity);
            boolean isDetected = manager.isHardwareDetected();
            boolean hasFingerprints = manager.hasEnrolledFingerprints();
            if (isDetected && hasFingerprints) {
                // 新指纹验证
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    android.os.CancellationSignal cancellationSignal = new android.os.CancellationSignal();
                    Executor executor = activity.getMainExecutor();
                    BiometricPrompt biometricPrompt = new BiometricPrompt.Builder(activity)
                            .setTitle("请验证指纹")
                            .setNegativeButton("取消", executor, (dialog, which) -> {
                                cancellationSignal.cancel();
                                emitter.onNext(false);
                            })
                            .build();
                    biometricPrompt.authenticate(cancellationSignal, executor, new BiometricPrompt.AuthenticationCallback() {

                        @Override
                        public void onAuthenticationSucceeded(BiometricPrompt.AuthenticationResult result) {
                            super.onAuthenticationSucceeded(result);
                            emitter.onNext(true);
                            emitter.onComplete();
                        }

                        @Override
                        public void onAuthenticationFailed() {
                            super.onAuthenticationFailed();
                            emitter.onError(new Exception("onAuthenticationFailed"));
                        }

                        @Override
                        public void onAuthenticationError(int errMsgId, CharSequence errString) {
                            emitter.onError(new Exception(errString.toString()));
                        }
                    });
                } else {
                    manager.authenticate(null, 0, cancellationSignal, new FingerprintManagerCompat.AuthenticationCallback() {
                        @Override
                        public void onAuthenticationSucceeded(FingerprintManagerCompat.AuthenticationResult result) {
                            emitter.onNext(true);
                            emitter.onComplete();
                        }

                        @Override
                        public void onAuthenticationFailed() {
                            emitter.onError(new Exception("onAuthenticationFailed"));
                        }

                        @Override
                        public void onAuthenticationError(int errMsgId, CharSequence errString) {
                            emitter.onError(new Exception(errString.toString()));
                        }
                    }, null);
                }
            }
        });
    }
}
