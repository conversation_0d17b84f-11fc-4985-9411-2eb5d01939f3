package com.haoxueren.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

public class MyPreferences {

    private final String MODE_API = "mode_api";
    private final String FINGERPRINT_ENABLED = "fingerprint_enabled";

    private MyPreferences() {
    }

    public static MyPreferences getInstance() {
        return InitFactory.INSTANCE;
    }

    private static class InitFactory {
        private static final MyPreferences INSTANCE = new MyPreferences();
    }

    private SharedPreferences getDefaultPreferences() {
        Context context = ContextManager.getContext();
        return PreferenceManager.getDefaultSharedPreferences(context);
    }

    public void setApiMode(boolean api) {
        SharedPreferences preferences = this.getDefaultPreferences();
        preferences.edit().putBoolean(MODE_API, api).apply();
    }


    public boolean getApiMode() {
        SharedPreferences preferences = this.getDefaultPreferences();
        return preferences.getBoolean(MODE_API, false);
    }

    public void setFingerprintEnabled(boolean enabled) {
        SharedPreferences preferences = this.getDefaultPreferences();
        preferences.edit().putBoolean(FINGERPRINT_ENABLED, enabled).apply();
    }

    public boolean isFingerprintEnabled() {
        SharedPreferences preferences = this.getDefaultPreferences();
        return preferences.getBoolean(FINGERPRINT_ENABLED, true);
    }
}
