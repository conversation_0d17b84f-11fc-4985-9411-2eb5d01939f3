package com.haoxueren.utils;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 数组工具类
 */
public class ArrayUtils {

    private ArrayUtils() {
    }

    /**
     * 判断两个数组是否有公共元素
     */
    public static boolean hasCommonElement(Object[] array1, Object[] array2) {
        if (array1 == null || array2 == null) {
            return false;
        }
        Map<Object, Integer> map = new HashMap<>();
        for (Object s : array1) {
            map.put(s, 1);
        }
        for (Object s : array2) {
            map.put(s, 1);
        }
        return map.size() < array1.length + array2.length;
    }

    /**
     * 判断两个集合是否有公共元素
     */
    public static boolean hasCommonElement(Collection<?> collection1, Collection<?> collection2) {
        if (collection1 == null || collection2 == null) {
            return false;
        }
        Map<Object, Integer> map = new HashMap<>();
        for (Object element : collection1) {
            map.put(element, 1);
        }
        for (Object element : collection2) {
            map.put(element, 1);
        }
        return map.size() < collection1.size() + collection2.size();
    }
}
