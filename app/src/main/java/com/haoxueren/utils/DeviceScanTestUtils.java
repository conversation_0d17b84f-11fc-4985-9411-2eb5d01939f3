package com.haoxueren.utils;

import android.util.Log;

import com.haoxueren.sqlite.DeviceDiscoveryManager;
import com.haoxueren.sqlite.DatabaseSyncManager;
import com.haoxueren.sqlite.EnhancedSocketManager;

import java.util.List;

/**
 * 设备扫描测试工具
 * 用于测试和调试设备发现功能
 */
public class DeviceScanTestUtils {
    
    private static final String TAG = "DeviceScanTest";
    
    /**
     * 执行完整的设备扫描测试
     */
    public static void performFullScanTest() {
        Log.d(TAG, "开始执行完整的设备扫描测试");
        
        // 1. 检查服务器状态
        boolean serverRunning = EnhancedSocketManager.isServerRunning();
        Log.d(TAG, "服务器运行状态: " + serverRunning);
        
        if (!serverRunning) {
            Log.d(TAG, "启动服务器...");
            EnhancedSocketManager.startEnhancedServer(2024);
        }
        
        // 2. 启动UDP响应服务器
        Log.d(TAG, "启动UDP响应服务器...");
        DeviceDiscoveryManager.getInstance().startUdpResponseServer();
        
        // 3. 获取本机网络信息
        String localIp = NetworkDiagnosticUtils.getLocalIpAddress();
        Log.d(TAG, "本机IP地址: " + localIp);
        
        // 4. 开始设备扫描
        Log.d(TAG, "开始设备扫描...");
        DeviceDiscoveryManager.getInstance().startDeviceDiscovery(new DeviceDiscoveryManager.DiscoveryCallback() {
            @Override
            public void onDeviceFound(DatabaseSyncManager.DeviceInfo device) {
                Log.d(TAG, "发现设备: " + device.name + " (" + device.ip + ":" + device.port + ")");
            }
            
            @Override
            public void onScanProgress(String message) {
                Log.d(TAG, "扫描进度: " + message);
            }
            
            @Override
            public void onScanComplete(List<DatabaseSyncManager.DeviceInfo> devices) {
                Log.d(TAG, "扫描完成，共发现 " + devices.size() + " 台设备");
                for (DatabaseSyncManager.DeviceInfo device : devices) {
                    Log.d(TAG, "  - " + device.name + " (" + device.ip + ":" + device.port + ")");
                }
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "扫描错误: " + error);
            }
        });
    }
    
    /**
     * 测试网络连通性
     */
    public static void testNetworkConnectivity() {
        Log.d(TAG, "开始测试网络连通性");
        
        NetworkDiagnosticUtils.diagnoseNetworkStatus()
            .subscribe(
                result -> Log.d(TAG, "网络诊断结果:\n" + result),
                error -> Log.e(TAG, "网络诊断失败", error)
            );
    }
    
    /**
     * 测试UDP广播
     */
    public static void testUdpBroadcast() {
        Log.d(TAG, "开始测试UDP广播");
        
        // 启动UDP响应服务器
        DeviceDiscoveryManager.getInstance().startUdpResponseServer();
        
        // 等待一秒后开始扫描
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            DeviceDiscoveryManager.getInstance().startDeviceDiscovery(new DeviceDiscoveryManager.DiscoveryCallback() {
                @Override
                public void onDeviceFound(DatabaseSyncManager.DeviceInfo device) {
                    Log.d(TAG, "UDP发现设备: " + device.name + " (" + device.ip + ")");
                }
                
                @Override
                public void onScanProgress(String message) {
                    Log.d(TAG, "UDP扫描: " + message);
                }
                
                @Override
                public void onScanComplete(List<DatabaseSyncManager.DeviceInfo> devices) {
                    Log.d(TAG, "UDP扫描完成，发现 " + devices.size() + " 台设备");
                }
                
                @Override
                public void onError(String error) {
                    Log.e(TAG, "UDP扫描错误: " + error);
                }
            });
        }, 1000);
    }
    
    /**
     * 检查常见问题
     */
    public static void checkCommonIssues() {
        Log.d(TAG, "检查常见问题");
        
        // 1. 检查网络连接
        String localIp = NetworkDiagnosticUtils.getLocalIpAddress();
        if (localIp == null) {
            Log.e(TAG, "问题: 无法获取本机IP地址，请检查WiFi连接");
            return;
        }
        Log.d(TAG, "✓ 本机IP地址正常: " + localIp);
        
        // 2. 检查服务器状态
        boolean serverRunning = EnhancedSocketManager.isServerRunning();
        if (!serverRunning) {
            Log.w(TAG, "警告: 服务器未运行，正在启动...");
            EnhancedSocketManager.startEnhancedServer(2024);
        } else {
            Log.d(TAG, "✓ 服务器运行正常");
        }
        
        // 3. 检查网络段
        String networkPrefix = localIp.substring(0, localIp.lastIndexOf('.'));
        Log.d(TAG, "✓ 网络段: " + networkPrefix + ".x");
        
        // 4. 测试本机连接
        NetworkDiagnosticUtils.testConnection(localIp, 2024)
            .subscribe(
                result -> Log.d(TAG, "本机连接测试: " + result),
                error -> Log.e(TAG, "本机连接测试失败", error)
            );
    }
}
