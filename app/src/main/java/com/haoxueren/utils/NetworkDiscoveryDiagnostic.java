package com.haoxueren.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

import com.haoxueren.pray.MyApplication;
import com.haoxueren.sqlite.DeviceDiscoveryManager;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 网络发现诊断工具
 * 专门用于诊断设备间网络发现问题
 */
public class NetworkDiscoveryDiagnostic {
    
    private static final String TAG = "NetworkDiagnostic";
    private static final int DISCOVERY_PORT = 2025;
    private static final int SYNC_PORT = 2024;
    private static final String DISCOVERY_MESSAGE = "HAOPRAY_DISCOVERY";
    private static final String DISCOVERY_RESPONSE = "HAOPRAY_DEVICE";
    
    /**
     * 全面诊断网络发现问题
     */
    public static Observable<String> diagnoseNetworkDiscovery(String targetIp) {
        return Observable.<String>create(emitter -> {
            StringBuilder result = new StringBuilder();
            
            try {
                result.append("=== 网络发现诊断报告 ===\n");
                result.append("目标设备IP: ").append(targetIp).append("\n\n");
                
                // 1. 基础网络检查
                result.append("1. 基础网络检查:\n");
                String networkStatus = checkBasicNetwork();
                result.append(networkStatus).append("\n");
                
                // 2. 端口可用性检查
                result.append("2. 端口可用性检查:\n");
                result.append(checkPortAvailability(DISCOVERY_PORT) ? "✓ UDP端口2025可用\n" : "✗ UDP端口2025被占用\n");
                result.append(checkPortAvailability(SYNC_PORT) ? "✓ TCP端口2024可用\n" : "✗ TCP端口2024被占用\n");
                
                // 3. UDP响应服务器状态检查
                result.append("\n3. UDP响应服务器状态:\n");
                boolean udpServerRunning = DeviceDiscoveryManager.getInstance().isUdpServerRunning();
                result.append(udpServerRunning ? "✓ UDP响应服务器正在运行\n" : "✗ UDP响应服务器未运行\n");
                
                if (!udpServerRunning) {
                    result.append("正在启动UDP响应服务器...\n");
                    DeviceDiscoveryManager.getInstance().startUdpResponseServer();
                    Thread.sleep(1000); // 等待服务器启动
                    result.append(DeviceDiscoveryManager.getInstance().isUdpServerRunning() ? 
                        "✓ UDP响应服务器启动成功\n" : "✗ UDP响应服务器启动失败\n");
                }
                
                // 4. 网络连通性测试
                result.append("\n4. 网络连通性测试:\n");
                String pingResult = testPing(targetIp);
                result.append(pingResult).append("\n");
                
                // 5. TCP连接测试
                result.append("\n5. TCP连接测试:\n");
                String tcpResult = testTcpConnection(targetIp, SYNC_PORT);
                result.append(tcpResult).append("\n");
                
                // 6. UDP发现测试
                result.append("\n6. UDP发现测试:\n");
                String udpResult = testUdpDiscovery(targetIp);
                result.append(udpResult).append("\n");
                
                // 7. 双向UDP测试
                result.append("\n7. 双向UDP测试:\n");
                String bidirectionalResult = testBidirectionalUdp(targetIp);
                result.append(bidirectionalResult).append("\n");
                
                result.append("\n=== 诊断完成 ===\n");
                
                emitter.onNext(result.toString());
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .timeout(30, TimeUnit.SECONDS);
    }
    
    /**
     * 检查基础网络状态
     */
    private static String checkBasicNetwork() {
        StringBuilder result = new StringBuilder();
        Context context = MyApplication.getContext();
        
        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            
            if (activeNetwork != null && activeNetwork.isConnected()) {
                result.append("✓ 网络已连接: ").append(activeNetwork.getTypeName()).append("\n");
                
                if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
                    WifiManager wifiManager = (WifiManager) context.getApplicationContext()
                        .getSystemService(Context.WIFI_SERVICE);
                    WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                    
                    String localIp = getLocalIpAddress();
                    result.append("✓ WiFi已连接: ").append(wifiInfo.getSSID()).append("\n");
                    result.append("✓ 本机IP: ").append(localIp).append("\n");
                } else {
                    result.append("⚠ 非WiFi连接，可能影响局域网发现\n");
                }
            } else {
                result.append("✗ 网络未连接\n");
            }
        } catch (Exception e) {
            result.append("✗ 网络检查失败: ").append(e.getMessage()).append("\n");
        }
        
        return result.toString();
    }
    
    /**
     * 检查端口可用性
     */
    private static boolean checkPortAvailability(int port) {
        try {
            ServerSocket serverSocket = new ServerSocket(port);
            serverSocket.close();
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 测试Ping连通性
     */
    private static String testPing(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            boolean reachable = address.isReachable(5000);
            return reachable ? "✓ Ping成功: " + ip : "✗ Ping失败: " + ip;
        } catch (Exception e) {
            return "✗ Ping错误: " + ip + " - " + e.getMessage();
        }
    }
    
    /**
     * 测试TCP连接
     */
    private static String testTcpConnection(String ip, int port) {
        try {
            Socket socket = new Socket();
            socket.connect(new java.net.InetSocketAddress(ip, port), 5000);
            socket.close();
            return "✓ TCP连接成功: " + ip + ":" + port;
        } catch (Exception e) {
            return "✗ TCP连接失败: " + ip + ":" + port + " - " + e.getMessage();
        }
    }
    
    /**
     * 测试UDP发现
     */
    private static String testUdpDiscovery(String targetIp) {
        StringBuilder result = new StringBuilder();
        DatagramSocket socket = null;
        
        try {
            socket = new DatagramSocket();
            socket.setBroadcast(true);
            socket.setSoTimeout(3000);
            
            // 发送发现消息
            byte[] buffer = DISCOVERY_MESSAGE.getBytes();
            InetAddress address = InetAddress.getByName(targetIp);
            DatagramPacket packet = new DatagramPacket(buffer, buffer.length, address, DISCOVERY_PORT);
            socket.send(packet);
            result.append("✓ UDP发现消息已发送到: ").append(targetIp).append(":").append(DISCOVERY_PORT).append("\n");
            
            // 等待响应
            byte[] responseBuffer = new byte[1024];
            DatagramPacket responsePacket = new DatagramPacket(responseBuffer, responseBuffer.length);
            socket.receive(responsePacket);
            
            String response = new String(responsePacket.getData(), 0, responsePacket.getLength());
            if (response.startsWith(DISCOVERY_RESPONSE)) {
                result.append("✓ 收到正确的UDP响应: ").append(response);
            } else {
                result.append("⚠ 收到未知UDP响应: ").append(response);
            }
            
        } catch (java.net.SocketTimeoutException e) {
            result.append("✗ UDP发现超时，未收到响应");
        } catch (Exception e) {
            result.append("✗ UDP发现失败: ").append(e.getMessage());
        } finally {
            if (socket != null) {
                socket.close();
            }
        }
        
        return result.toString();
    }
    
    /**
     * 测试双向UDP通信
     */
    private static String testBidirectionalUdp(String targetIp) {
        StringBuilder result = new StringBuilder();
        
        // 测试发送到目标设备
        result.append("发送测试: ");
        result.append(testUdpDiscovery(targetIp)).append("\n");
        
        // 测试从目标设备接收（需要目标设备主动发送）
        result.append("接收测试: 请在目标设备上执行发现操作...\n");
        
        return result.toString();
    }
    
    /**
     * 获取本机IP地址
     */
    private static String getLocalIpAddress() {
        try {
            Context context = MyApplication.getContext();
            WifiManager wifiManager = (WifiManager) context.getApplicationContext()
                .getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            int ipAddress = wifiInfo.getIpAddress();
            
            return String.format("%d.%d.%d.%d",
                (ipAddress & 0xff),
                (ipAddress >> 8 & 0xff),
                (ipAddress >> 16 & 0xff),
                (ipAddress >> 24 & 0xff));
        } catch (Exception e) {
            return "";
        }
    }
    
    /**
     * 修复网络发现问题
     */
    public static Observable<String> fixNetworkDiscoveryIssues() {
        return Observable.<String>create(emitter -> {
            StringBuilder result = new StringBuilder();
            
            try {
                result.append("=== 网络发现问题修复 ===\n");
                
                // 1. 重启UDP响应服务器
                result.append("1. 重启UDP响应服务器...\n");
                DeviceDiscoveryManager.getInstance().stopUdpResponseServer();
                Thread.sleep(1000);
                DeviceDiscoveryManager.getInstance().startUdpResponseServer();
                Thread.sleep(2000);
                
                boolean serverRunning = DeviceDiscoveryManager.getInstance().isUdpServerRunning();
                result.append(serverRunning ? "✓ UDP响应服务器重启成功\n" : "✗ UDP响应服务器重启失败\n");
                
                // 2. 清理网络状态
                result.append("\n2. 清理网络状态...\n");
                // 这里可以添加更多的清理逻辑
                result.append("✓ 网络状态清理完成\n");
                
                result.append("\n=== 修复完成 ===\n");
                result.append("建议重新进行设备发现测试\n");
                
                emitter.onNext(result.toString());
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread());
    }
}
