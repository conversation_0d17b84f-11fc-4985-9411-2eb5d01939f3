package com.haoxueren.utils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.UnknownHostException;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

public class RxPing {

    /**
     * 判断是否有外网连接（普通方法不能判断外网的网络是否连接，比如连接上局域网）
     * copy from: https://www.cnblogs.com/langfei8818/p/7449664.html
     *
     * @return
     */
    public static Observable<String> ping(String url) {
        return Observable.create((ObservableOnSubscribe<String>) emitter -> {
            // 其中-c 1 是代表次数 -w 1 代表的是时间，单位是秒，后面接服务器的地址
            String command = String.format("ping -c 1 -w 10 %s", url);
            Process process = Runtime.getRuntime().exec(command);
            // 读取 ping 的内容
            InputStream inputStream = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder builder = new StringBuilder();
            String line = reader.readLine();
            while (line != null) {
                builder.append(line);
                line = reader.readLine();
            }
            // ping 的结果
            int status = process.waitFor();
            if (status == 0) {
                emitter.onNext(builder.toString());
            } else {
                emitter.onError(new UnknownHostException("Can't connect to " + url));
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }


}
