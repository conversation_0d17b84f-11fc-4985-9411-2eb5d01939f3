package com.haoxueren.utils;

import android.util.Log;

import com.haoxueren.sqlite.DeviceDiscoveryManager;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;

/**
 * 快速网络测试工具
 * 用于快速验证网络发现功能是否正常
 */
public class QuickNetworkTest {
    
    private static final String TAG = "QuickNetworkTest";
    private static final int DISCOVERY_PORT = 2025;
    private static final String DISCOVERY_MESSAGE = "HAOPRAY_DISCOVERY";
    private static final String DISCOVERY_RESPONSE = "HAOPRAY_DEVICE";
    
    /**
     * 快速测试设备A到设备B的发现
     * 在设备A上调用，测试能否发现设备B
     */
    public static void testDiscoverDeviceB() {
        testDiscoverDevice("************", "设备B");
    }
    
    /**
     * 快速测试设备B到设备A的发现
     * 在设备B上调用，测试能否发现设备A
     */
    public static void testDiscoverDeviceA() {
        testDiscoverDevice("***********", "设备A");
    }
    
    /**
     * 测试发现指定设备
     */
    public static void testDiscoverDevice(String targetIp, String deviceName) {
        new Thread(() -> {
            Log.d(TAG, "开始测试发现" + deviceName + " (" + targetIp + ")");
            
            DatagramSocket socket = null;
            try {
                // 1. 检查本地UDP响应服务器状态
                boolean serverRunning = DeviceDiscoveryManager.getInstance().isUdpServerRunning();
                Log.d(TAG, "本地UDP响应服务器状态: " + (serverRunning ? "运行中" : "未运行"));
                
                if (!serverRunning) {
                    Log.d(TAG, "启动UDP响应服务器...");
                    DeviceDiscoveryManager.getInstance().startUdpResponseServer();
                    Thread.sleep(2000); // 等待服务器启动
                }
                
                // 2. 发送UDP发现消息
                socket = new DatagramSocket();
                socket.setBroadcast(true);
                socket.setSoTimeout(5000); // 5秒超时
                
                byte[] buffer = DISCOVERY_MESSAGE.getBytes();
                InetAddress address = InetAddress.getByName(targetIp);
                DatagramPacket packet = new DatagramPacket(buffer, buffer.length, address, DISCOVERY_PORT);
                
                Log.d(TAG, "发送UDP发现消息到: " + targetIp + ":" + DISCOVERY_PORT);
                socket.send(packet);
                
                // 3. 等待响应
                byte[] responseBuffer = new byte[1024];
                DatagramPacket responsePacket = new DatagramPacket(responseBuffer, responseBuffer.length);
                
                try {
                    socket.receive(responsePacket);
                    String response = new String(responsePacket.getData(), 0, responsePacket.getLength());
                    String responseIp = responsePacket.getAddress().getHostAddress();
                    
                    Log.d(TAG, "收到响应: " + response + " 来自: " + responseIp);
                    
                    if (response.startsWith(DISCOVERY_RESPONSE)) {
                        Log.i(TAG, "✓ 成功发现" + deviceName + " (" + responseIp + ")");
                    } else {
                        Log.w(TAG, "⚠ 收到未知响应: " + response);
                    }
                    
                } catch (java.net.SocketTimeoutException e) {
                    Log.e(TAG, "✗ 发现" + deviceName + "超时，未收到响应");
                }
                
            } catch (Exception e) {
                Log.e(TAG, "✗ 发现" + deviceName + "失败", e);
            } finally {
                if (socket != null) {
                    socket.close();
                }
            }
        }).start();
    }
    
    /**
     * 测试UDP响应服务器
     */
    public static void testUdpResponseServer() {
        new Thread(() -> {
            Log.d(TAG, "开始测试UDP响应服务器");
            
            try {
                // 检查服务器状态
                boolean serverRunning = DeviceDiscoveryManager.getInstance().isUdpServerRunning();
                Log.d(TAG, "UDP响应服务器状态: " + (serverRunning ? "运行中" : "未运行"));
                
                if (!serverRunning) {
                    Log.d(TAG, "启动UDP响应服务器...");
                    DeviceDiscoveryManager.getInstance().startUdpResponseServer();
                    Thread.sleep(2000);
                    
                    serverRunning = DeviceDiscoveryManager.getInstance().isUdpServerRunning();
                    Log.d(TAG, "启动后状态: " + (serverRunning ? "运行中" : "启动失败"));
                }
                
                if (serverRunning) {
                    Log.i(TAG, "✓ UDP响应服务器正常运行");
                    
                    // 测试自我发现
                    testSelfDiscovery();
                } else {
                    Log.e(TAG, "✗ UDP响应服务器启动失败");
                }
                
            } catch (Exception e) {
                Log.e(TAG, "✗ UDP响应服务器测试失败", e);
            }
        }).start();
    }
    
    /**
     * 测试自我发现（发送消息给自己）
     */
    private static void testSelfDiscovery() {
        DatagramSocket socket = null;
        try {
            String localIp = NetworkDiagnosticUtils.getLocalIpAddress();
            if (localIp == null || localIp.isEmpty()) {
                Log.e(TAG, "无法获取本机IP地址");
                return;
            }
            
            Log.d(TAG, "测试自我发现，本机IP: " + localIp);
            
            socket = new DatagramSocket();
            socket.setSoTimeout(3000);
            
            // 发送发现消息给自己
            byte[] buffer = DISCOVERY_MESSAGE.getBytes();
            InetAddress address = InetAddress.getByName(localIp);
            DatagramPacket packet = new DatagramPacket(buffer, buffer.length, address, DISCOVERY_PORT);
            socket.send(packet);
            
            // 等待响应
            byte[] responseBuffer = new byte[1024];
            DatagramPacket responsePacket = new DatagramPacket(responseBuffer, responseBuffer.length);
            socket.receive(responsePacket);
            
            String response = new String(responsePacket.getData(), 0, responsePacket.getLength());
            Log.d(TAG, "自我发现响应: " + response);
            
            if (response.startsWith(DISCOVERY_RESPONSE)) {
                Log.i(TAG, "✓ 自我发现成功，UDP响应服务器工作正常");
            } else {
                Log.w(TAG, "⚠ 自我发现收到未知响应: " + response);
            }
            
        } catch (java.net.SocketTimeoutException e) {
            Log.e(TAG, "✗ 自我发现超时");
        } catch (Exception e) {
            Log.e(TAG, "✗ 自我发现失败", e);
        } finally {
            if (socket != null) {
                socket.close();
            }
        }
    }
    
    /**
     * 运行完整的网络测试套件
     */
    public static void runFullNetworkTest() {
        Log.d(TAG, "=== 开始完整网络测试 ===");
        
        // 1. 测试UDP响应服务器
        testUdpResponseServer();
        
        // 等待2秒
        new Thread(() -> {
            try {
                Thread.sleep(3000);
                
                // 2. 测试发现设备A
                testDiscoverDeviceA();
                
                Thread.sleep(2000);
                
                // 3. 测试发现设备B
                testDiscoverDeviceB();
                
                Thread.sleep(2000);
                
                Log.d(TAG, "=== 网络测试完成 ===");
                
            } catch (InterruptedException e) {
                Log.e(TAG, "测试被中断", e);
            }
        }).start();
    }
}
