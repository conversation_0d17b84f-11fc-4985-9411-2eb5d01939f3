package com.haoxueren.utils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * 文本摘要算法，支持生成md5/sha256摘要
 */
public class DigestUtils {

    public static String sha256(String source) throws Exception {
        byte[] result = DigestUtils.sha256(source.getBytes(StandardCharsets.UTF_8));
        return new BigInteger(1, result).toString(16);
    }

    /**
     * SHA-256摘要，返回32位字节数组
     */
    public static byte[] sha256(byte[] source) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        digest.update(source);
        return digest.digest();
    }

    /**
     * MD5摘要，返回16位字节数组
     */
    public static byte[] md5(byte[] source) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        digest.update(source);
        return digest.digest();
    }

    /**
     * MD5摘要，返回32位字符串
     */
    public static String md5(String source) {
        byte[] result;
        try {
            result = DigestUtils.md5(source.getBytes(StandardCharsets.UTF_8));
            return new BigInteger(1, result).toString(16);
        } catch (Exception e) {
            e.printStackTrace();
            return System.currentTimeMillis() + "";
        }
    }

}
