package com.haoxueren.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DateUtils {

    /**
     * return today like 2019.06.23
     */
    public static String today() {
        return DateUtils.today("yyyy.MM.dd");
    }

    public static String today(String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern, Locale.CHINESE);
        return dateFormat.format(new Date());
    }

    /**
     * 格式化时间戳
     */
    public static String format(long timestamp, String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern, Locale.CHINESE);
        return dateFormat.format(new Date(timestamp));
    }
}
