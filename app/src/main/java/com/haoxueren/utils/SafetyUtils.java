package com.haoxueren.utils;

import android.app.Activity;
import android.text.TextUtils;

import com.haoxueren.dialog.AuthCodeDialog;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.function.Consumer;

/**
 * 安全工具类
 */
public class SafetyUtils {

    public static String getAuthCode() {
        SimpleDateFormat format = new SimpleDateFormat("yyMMdd", Locale.CHINA);
        String date = format.format(new Date());
        CharSequence dateCode = new StringBuilder(date).reverse();
        return dateCode.toString();
    }

    /**
     * 检查当前设备是否合法
     */
    public static void checkAuthCode(Activity activity, Consumer<Boolean> dismiss) {
        String dynamicCode = SafetyUtils.getAuthCode();
        String localCode = PreferenceUtils.getString("auth_code");
        if (!TextUtils.equals(dynamicCode, localCode)) {
            AuthCodeDialog dialog = new AuthCodeDialog(activity);
            dialog.show(dismiss);
        } else {
            dismiss.accept(true);
        }
    }
}
