package com.haoxueren.utils;

import android.graphics.Point;
import android.graphics.Rect;
import android.view.Display;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;

import com.haoxueren.java8.Action;

public class DisplayUtils {

    public static void onLayoutChanged(View view, Action action) {
        view.getViewTreeObserver().addOnGlobalLayoutListener(action::run);
    }

    /**
     * 获取View所在窗口的可见区域
     */
    public static Rect getWindowVisibleRect(View view) {
        Rect visibleRect = new Rect();
        view.getWindowVisibleDisplayFrame(visibleRect);
        return visibleRect;
    }

    public static Rect getScreenRect(WindowManager manager) {
        Display display = manager.getDefaultDisplay();
        Point sizePoint = new Point();
        display.getRealSize(sizePoint);
        return new Rect(0, 0, sizePoint.x, sizePoint.y);
    }


}
