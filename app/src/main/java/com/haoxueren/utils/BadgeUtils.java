package com.haoxueren.utils;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;

public class BadgeUtils {

    /**
     * 在桌面图标上显示小红点
     * https://juejin.cn/post/7106684952195891208
     */
    public static void showBadge(Context context, int count) {
        try {
            // 获取应用包名
            String packageName = context.getPackageName();
            // 获取启动Activity的全类名
            String launchClassName = context.getPackageManager()
                    .getLaunchIntentForPackage(packageName)
                    .getComponent().getClassName();
            Bundle bundle = new Bundle();
            bundle.putString("package", packageName);
            bundle.putString("class", launchClassName);
            bundle.putInt("badgenumber", count);
            Uri uri = Uri.parse("content://com.huawei.android.launcher.settings/badge/");
            context.getContentResolver()
                    .call(uri, "change_badge", null, bundle);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
