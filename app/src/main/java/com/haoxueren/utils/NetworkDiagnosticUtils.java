package com.haoxueren.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.text.TextUtils;

import com.haoxueren.pray.MyApplication;

import java.io.IOException;
import java.net.InetAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 网络诊断工具类
 * 用于诊断设备扫描问题
 */
public class NetworkDiagnosticUtils {

    /**
     * 诊断网络连接状态
     */
    public static Observable<String> diagnoseNetworkStatus() {
        return Observable.<String>create(emitter -> {
            Context context = MyApplication.getContext();
            StringBuilder result = new StringBuilder();
            
            try {
                // 1. 检查网络连接状态
                ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
                NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
                
                if (activeNetwork != null && activeNetwork.isConnected()) {
                    result.append("✓ 网络已连接: ").append(activeNetwork.getTypeName()).append("\n");
                    
                    if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
                        // 2. 检查WiFi信息
                        WifiManager wifiManager = (WifiManager) context.getApplicationContext()
                            .getSystemService(Context.WIFI_SERVICE);
                        
                        if (wifiManager != null) {
                            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                            String ssid = wifiInfo.getSSID();
                            int ipAddress = wifiInfo.getIpAddress();
                            
                            if (ipAddress != 0) {
                                String localIp = String.format("%d.%d.%d.%d",
                                    (ipAddress & 0xff),
                                    (ipAddress >> 8 & 0xff),
                                    (ipAddress >> 16 & 0xff),
                                    (ipAddress >> 24 & 0xff));
                                
                                result.append("✓ WiFi SSID: ").append(ssid).append("\n");
                                result.append("✓ 本机IP: ").append(localIp).append("\n");
                                
                                // 3. 检查网络段
                                String networkPrefix = localIp.substring(0, localIp.lastIndexOf('.'));
                                result.append("✓ 网络段: ").append(networkPrefix).append(".x\n");
                                
                            } else {
                                result.append("✗ 无法获取IP地址\n");
                            }
                        }
                    }
                } else {
                    result.append("✗ 网络未连接\n");
                }
                
                // 4. 检查端口占用情况
                result.append("\n端口检查:\n");
                result.append(checkPort(2024) ? "✓ 端口2024可用\n" : "✗ 端口2024被占用\n");
                result.append(checkPort(2025) ? "✓ 端口2025可用\n" : "✗ 端口2025被占用\n");
                
                emitter.onNext(result.toString());
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * 检查端口是否可用
     */
    private static boolean checkPort(int port) {
        try {
            ServerSocket serverSocket = new ServerSocket(port);
            serverSocket.close();
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 测试连接到指定IP和端口
     */
    public static Observable<String> testConnection(String ip, int port) {
        return Observable.<String>create(emitter -> {
            try {
                Socket socket = new Socket();
                socket.connect(new java.net.InetSocketAddress(ip, port), 5000);
                socket.close();
                emitter.onNext("✓ 连接成功: " + ip + ":" + port);
            } catch (Exception e) {
                emitter.onNext("✗ 连接失败: " + ip + ":" + port + " - " + e.getMessage());
            }
            emitter.onComplete();
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .timeout(10, TimeUnit.SECONDS);
    }
    
    /**
     * Ping测试
     */
    public static Observable<String> pingTest(String ip) {
        return Observable.<String>create(emitter -> {
            try {
                InetAddress address = InetAddress.getByName(ip);
                boolean reachable = address.isReachable(5000);
                emitter.onNext(reachable ? "✓ Ping成功: " + ip : "✗ Ping失败: " + ip);
            } catch (Exception e) {
                emitter.onNext("✗ Ping错误: " + ip + " - " + e.getMessage());
            }
            emitter.onComplete();
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .timeout(10, TimeUnit.SECONDS);
    }
    
    /**
     * 获取本机IP地址
     */
    public static String getLocalIpAddress() {
        try {
            Context context = MyApplication.getContext();
            WifiManager wifiManager = (WifiManager) context.getApplicationContext()
                .getSystemService(Context.WIFI_SERVICE);
            
            if (wifiManager != null) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                int ipAddress = wifiInfo.getIpAddress();
                
                if (ipAddress != 0) {
                    return String.format("%d.%d.%d.%d",
                        (ipAddress & 0xff),
                        (ipAddress >> 8 & 0xff),
                        (ipAddress >> 16 & 0xff),
                        (ipAddress >> 24 & 0xff));
                }
            }
            
            // 备用方法：通过网络接口获取
            java.util.Enumeration<java.net.NetworkInterface> interfaces = 
                java.net.NetworkInterface.getNetworkInterfaces();
            
            while (interfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = interfaces.nextElement();
                if (!networkInterface.isLoopback() && networkInterface.isUp()) {
                    java.util.Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress address = addresses.nextElement();
                        if (!address.isLoopbackAddress() && address instanceof java.net.Inet4Address) {
                            return address.getHostAddress();
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return null;
    }
}
