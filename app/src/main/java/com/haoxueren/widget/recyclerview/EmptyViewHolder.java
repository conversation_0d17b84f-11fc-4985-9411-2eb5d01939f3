package com.haoxueren.widget.recyclerview;

import android.view.View;
import android.view.ViewGroup;

import com.haoxueren.proxy.SuperViewHolder;

/**
 * 列表没有数据时，显示空状态布局
 * create by haomi<PERSON><PERSON><PERSON> on 2020/4/23
 */
public class EmptyViewHolder extends SuperViewHolder {

    public EmptyViewHolder(ViewGroup parent, int itemLayout) {
        super(parent, itemLayout);
    }

    @Override
    public void initView(View layout) {

    }

    @Override
    public void updateItem(Object bean) {

    }
}
