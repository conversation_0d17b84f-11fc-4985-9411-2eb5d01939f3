package com.haoxueren.proxy;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;

public class OutputStreamProxy {

    private OutputStream outputStream;

    public OutputStreamProxy(OutputStream outputStream) {
        this.outputStream = outputStream;
    }

    public void write(String text) throws IOException {
        OutputStreamWriter streamWriter = new OutputStreamWriter(outputStream);
        streamWriter.write(text);
        streamWriter.flush();
    }

    public void write(File file) throws IOException {
        FileInputStream fileInputStream = new FileInputStream(file);
        byte[] bytes = new byte[1024];
        int length = fileInputStream.read(bytes);
        while (length != -1) {
            outputStream.write(bytes, 0, length);
            length = fileInputStream.read(bytes);
        }
    }

    public void close() throws IOException {
        outputStream.close();
    }
}
