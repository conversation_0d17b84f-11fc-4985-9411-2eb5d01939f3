package com.haoxueren.proxy;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

/**
 * RecyclerView代理类，支持多布局
 */
public abstract class RecyclerViewProxy<T> extends ViewProxy {

    private RecyclerView recyclerView;
    private RecyclerAdapter adapter;
    private List<T> adapterList;

    public RecyclerViewProxy(RecyclerView recyclerView) {
        super(recyclerView);
        this.recyclerView = recyclerView;
        recyclerView.setLayoutManager(createLayoutManager());
    }

    public RecyclerViewProxy(View layout, int recyclerViewId) {
        this(layout.findViewById(recyclerViewId));
    }

    protected abstract SuperViewHolder<T> onCreateHolder(ViewGroup parent, int viewType);

    protected RecyclerView.LayoutManager createLayoutManager() {
        return new LinearLayoutManager(recyclerView.getContext());
    }

    protected int getItemType(int position) {
        return 0;
    }

    public Context getContext() {
        return recyclerView.getContext();
    }

    public void setAdapter(List<T> list) {
        this.adapterList = list;
        adapter = new RecyclerAdapter(list);
        recyclerView.setAdapter(adapter);
    }

    public List<T> getAdapterList() {
        return adapterList;
    }

    public void notifyDataSetChanged() {
        adapter.notifyDataSetChanged();
    }

    public void notifyItemRemoved(int position) {
        adapter.notifyItemRemoved(position);
    }

    public void notifyItemInserted(int position) {
        adapter.notifyItemInserted(position);
    }

    public void notifyItemChanged(int position) {
        adapter.notifyItemChanged(position);
    }

    /**
     * 滑动指定item到顶部
     */
    public void scrollToPosition(int position) {
        LinearSmoothScroller scroller = new LinearSmoothScroller(getContext()) {

            @Override
            protected int getHorizontalSnapPreference() {
                return LinearSmoothScroller.SNAP_TO_START;
            }

            @Override
            protected int getVerticalSnapPreference() {
                return LinearSmoothScroller.SNAP_TO_START;
            }
        };
        scroller.setTargetPosition(position);
        recyclerView.getLayoutManager().startSmoothScroll(scroller);
    }

    public RecyclerAdapter getAdapter() {
        return adapter;
    }

    private class RecyclerAdapter extends RecyclerView.Adapter<SuperViewHolder<T>> {

        private final List<T> list;

        public RecyclerAdapter(List<T> list) {
            this.list = list;
        }

        @Override
        public int getItemViewType(int position) {
            return getItemType(position);
        }

        @NonNull
        @Override
        public SuperViewHolder<T> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return onCreateHolder(parent, viewType);
        }

        @Override
        public void onBindViewHolder(@NonNull SuperViewHolder<T> holder, int position) {
            holder.updateItem(list.get(position));
        }

        @Override
        public int getItemCount() {
            return list.size();
        }
    }
}
