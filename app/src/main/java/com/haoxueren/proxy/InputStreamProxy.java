package com.haoxueren.proxy;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.function.Consumer;

public class InputStreamProxy {

    private InputStream inputStream;

    public InputStreamProxy(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    /**
     * 读取为文本
     */
    public String readString() throws IOException {
        InputStreamReader streamReader = new InputStreamReader(inputStream);
        BufferedReader bufferedReader = new BufferedReader(streamReader);
        StringBuilder builder = new StringBuilder();
        String line = bufferedReader.readLine();
        while (line != null) {
            builder.append(line);
            line = bufferedReader.readLine();
        }
        return builder.toString();
    }

    /**
     * 读取为文件
     */
    public File readFile(File file) throws IOException {
        FileOutputStream fileInputStream = new FileOutputStream(file);
        byte[] bytes = new byte[1024];
        int length = inputStream.read(bytes);
        while (length != -1) {
            fileInputStream.write(bytes, 0, length);
            length = inputStream.read(bytes);
        }
        return file;
    }

    /**
     * 读取为文件
     */
    public File readFile(File file, Consumer<Long> progress) throws IOException {
        FileOutputStream fileInputStream = new FileOutputStream(file);
        byte[] bytes = new byte[1024];
        int length = inputStream.read(bytes);
        while (length != -1) {
            fileInputStream.write(bytes, 0, length);
            progress.accept(file.length());
            length = inputStream.read(bytes);
        }
        return file;
    }

    public void close() throws IOException {
        inputStream.close();
    }
}
