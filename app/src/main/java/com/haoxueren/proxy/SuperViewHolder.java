package com.haoxueren.proxy;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import java.util.function.Consumer;


/**
 * create by haomingliang on 2020/4/14
 */
public abstract class SuperViewHolder<T> extends RecyclerView.ViewHolder {

    public SuperViewHolder(ViewGroup parent, int layoutRes) {
        super(LayoutInflater.from(parent.getContext())
                .inflate(layoutRes, parent, false));
        initView(itemView);
    }

    public abstract void initView(View layout);

    public abstract void updateItem(T bean);

}
