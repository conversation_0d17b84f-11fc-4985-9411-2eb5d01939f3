package com.haoxueren.proxy;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

/**
 * PopupWindow框架
 */
public abstract class PopupWindowProxy {

    private final PopupWindow popupWindow;

    /**
     * 根据内容自适应宽高
     */
    public PopupWindowProxy(Context context) {
        this(context, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    /**
     * 自定义PopupWindow宽高
     */
    public PopupWindowProxy(Context context, int width, int height) {
        popupWindow = new PopupWindow(width, height);
        popupWindow.setBackgroundDrawable(new ColorDrawable());
        popupWindow.setOutsideTouchable(true);
        View contentView = View.inflate(context, getLayoutId(), null);
        popupWindow.setContentView(contentView);
        this.initView(contentView);
    }

    protected abstract int getLayoutId();

    protected abstract void initView(View contentView);

    public PopupWindow getOrigin() {
        return popupWindow;
    }

    public void setAnimationStyle(int animationStyle) {
        popupWindow.setAnimationStyle(animationStyle);
    }

    public void show(View anchor) {
        if (popupWindow != null) {
            popupWindow.showAsDropDown(anchor);
        }
    }

    public void show(View anchor, int gravity) {
        this.show(anchor, 0, 0, gravity);
    }

    public void show(View anchor, int xoff, int yoff) {
        if (popupWindow != null) {
            popupWindow.showAsDropDown(anchor, xoff, yoff);
        }
    }

    public void show(View anchor, int xoff, int yoff, int gravity) {
        if (popupWindow != null) {
            popupWindow.showAsDropDown(anchor, xoff, yoff, gravity);
        }
    }

    public void showAtLocation(View parent, int x, int y) {
        if (popupWindow != null) {
            popupWindow.showAtLocation(parent, Gravity.TOP | Gravity.START, x, y);
        }
    }

    public boolean isShowing() {
        return popupWindow.isShowing();
    }

    public void dismiss() {
        if (popupWindow != null) {
            popupWindow.dismiss();
        }
    }

    protected void setOutsideTouchable(boolean touchable) {
        popupWindow.setOutsideTouchable(touchable);
    }
}