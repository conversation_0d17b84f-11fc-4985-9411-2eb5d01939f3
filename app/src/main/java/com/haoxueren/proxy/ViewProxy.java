package com.haoxueren.proxy;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.view.MotionEvent;
import android.view.View;

public class ViewProxy {

    private View view;

    public ViewProxy(View view) {
        this.view = view;
    }

    public ViewProxy(View layout, int id) {
        this(layout.findViewById(id));
    }

    public <T extends View> T getOrigin() {
        return (T) view;
    }

    public int getWidth() {
        return view.getWidth();
    }

    public void setVisible(boolean visible) {
        view.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setEnabled(boolean enable) {
        view.setAlpha(enable ? 1f : 0.7f);
        view.setEnabled(enable);
    }

    public void setAlpha(float alpha) {
        view.setAlpha(alpha);
    }

    /**
     * 点击时背影变色
     */
    public void setOnClickListener(View.OnClickListener listener) {
        this.setOnClickEventListener(new OnTouchEventListener() {
            @Override
            public void onDown(MotionEvent event) {
                view.setAlpha(0.7f);
            }

            @Override
            public void onUp(MotionEvent event) {
                view.setAlpha(1f);
                listener.onClick(view);
            }

            @Override
            public void onCancel(MotionEvent event) {
                view.setAlpha(1f);
            }
        });
    }

    public void setOnClickEventListener(OnTouchEventListener listener) {
        view.setOnTouchListener(new View.OnTouchListener() {

            private boolean isTouchInView(MotionEvent event) {
                float width = view.getWidth();
                float height = view.getHeight();
                return event.getX() >= 0 && event.getX() <= width
                        && event.getY() >= 0 && event.getY() <= height;
            }

            @Override
            public boolean onTouch(View view, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        listener.onDown(event);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (!isTouchInView(event)) {
                            listener.onCancel(event);
                            return true;
                        }
                        break;
                    case MotionEvent.ACTION_CANCEL:
                        listener.onCancel(event);
                        break;
                    case MotionEvent.ACTION_UP:
                        if (isTouchInView(event)) {
                            listener.onUp(event);
                        } else {
                            listener.onCancel(event);
                        }
                        break;
                }
                return true;
            }
        });
    }

    /**
     * 目前仅支持Bitmap背景
     */
    public void setRoundCorners(int radius) {
        Drawable background = view.getBackground();
        if (background instanceof BitmapDrawable) {
            BitmapDrawable drawable = (BitmapDrawable) background;
            Bitmap bitmap = drawable.getBitmap();
            Bitmap roundBitmap = this.getRoundBitmap(bitmap, radius);
            BitmapDrawable roundBackground = new BitmapDrawable(view.getContext().getResources(), roundBitmap);
            view.setBackground(roundBackground);
        }

    }

    /**
     * 绘制圆角矩形图片
     */
    private Bitmap getRoundBitmap(Bitmap bitmap, int radius) {
        // 创建一个新的Bitmap
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        Bitmap newBitmap = Bitmap.createBitmap(width, height, bitmap.getConfig());
        // 绘制一个圆角矩形
        Canvas canvas = new Canvas(newBitmap);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        RectF rectF = new RectF(0, 0, width, height);
        canvas.drawRoundRect(rectF, radius, radius, paint);
        // 绘制圆角矩形与图片的交集
        PorterDuffXfermode mode = new PorterDuffXfermode(PorterDuff.Mode.SRC_IN); // 取交集
        paint.setXfermode(mode);
        canvas.drawBitmap(bitmap, new Matrix(), paint); // 注：不用Matrix图片缩放有问题
        return newBitmap;
    }

    public interface OnTouchEventListener {

        void onDown(MotionEvent event);

        void onUp(MotionEvent event);

        void onCancel(MotionEvent event);
    }


}
