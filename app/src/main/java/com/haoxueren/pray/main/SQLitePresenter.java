package com.haoxueren.pray.main;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import com.haoxueren.pray.MyApplication;
import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.group.HaoPrayGroup;
import com.haoxueren.pray.service.DataService;
import com.haoxueren.sqlite.SQLiteHelper;
import com.haoxueren.utils.BadgeUtils;
import com.haoxueren.utils.DateUtils;
import com.haoxueren.utils.Singleton;

import java.text.MessageFormat;
import java.util.Calendar;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import io.reactivex.Observable;

/**
 * SQLite数据库交互
 */
public class SQLitePresenter implements MainPresenter {

    @Override
    public Observable<String> updateTitle() {
        SQLiteDatabase database = SQLiteHelper.getInstance().getDatabase();
        Cursor prayCursor = database.rawQuery("select * from HaoPray where date=?", new String[]{DateUtils.today()});
        int count = prayCursor.getCount();
        prayCursor.close();
        Cursor groupCursor = database.rawQuery("select groupId,count(*) from HaoGroup where groupId>=0 group by groupId", null);
        int groupCount = groupCursor.getCount();
        groupCursor.close();
        BadgeUtils.showBadge(MyApplication.getContext(), groupCount - count);
        return Observable.just(MessageFormat.format("目标培育计划({0}/{1})", count, groupCount));
    }

    @Override
    public Observable<String> getRandomId() {
        HashSet<String> todayIdSet = new HashSet<>();
        SQLiteDatabase database = SQLiteHelper.getInstance().getDatabase();
        Cursor cursor = database.rawQuery("select id from HaoPray where date=?", new String[]{DateUtils.today()});
        while (cursor.moveToNext()) {
            todayIdSet.add(cursor.getString(0));
        }
        cursor.close();
        return DataService.getInstance().getGroupIdMap(todayIdSet).doOnNext(map -> { // 去除未启用的分组
            Iterator<Map.Entry<Integer, Set<String>>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Integer groupId = iterator.next().getKey();
                if (groupId < 0) {
                    iterator.remove(); // 删除小于0的分组
                }
            }
        }).map(map -> {
            if (map.isEmpty()) {
                return "";
            }
            Random random = Singleton.getRandom();
            Integer[] groupIds = map.keySet().toArray(new Integer[0]);
            int groupIndex = random.nextInt(groupIds.length);
            Integer groupId = groupIds[groupIndex];
            Set<String> prayIdSet = map.get(groupId);
            String[] prayIdArray = prayIdSet.toArray(new String[0]);
            int dayOfYear = Calendar.getInstance().get(Calendar.DAY_OF_YEAR);
            int prayIndex = dayOfYear % prayIdArray.length; // 按天轮流显示
            return prayIdArray[prayIndex];
        });
    }

    @Override
    public Observable<Set<String>> queryTodayIds() {
        HashSet<String> set = new HashSet<>();
        SQLiteDatabase database = SQLiteHelper.getInstance().getDatabase();
        Cursor cursor = database.rawQuery("select id from HaoPray where date=?", new String[]{DateUtils.today()});
        while (cursor.moveToNext()) {
            set.add(cursor.getString(0));
        }
        cursor.close();
        return Observable.just(set);
    }

    @Override
    public Observable<String> saveHaoPray(HaoPray haoPray) {
        if (TextUtils.isEmpty(haoPray.getId())
                || TextUtils.isEmpty(haoPray.getDate())
                || TextUtils.isEmpty(haoPray.getPray())) {
            return Observable.error(new NullPointerException("必填字段不可为空"));
        }
        return SQLiteHelper.getInstance().insertHaoPray(haoPray);
    }

    @Override
    public Observable<String> saveHaoGroup(Integer groupId, String prayId, String pray) {
        long rowId = SQLiteHelper.getInstance().insertHaoGroup(groupId, prayId, pray);
        if (rowId < 0) {
            return Observable.error(new Exception("insert failed"));
        }
        return Observable.just("success");
    }

    @Override
    public Observable<List<HaoPrayGroup>> queryGroup(int fromId) {
        return Observable.just(SQLiteHelper.getInstance().queryHaoGroup(fromId));
    }

    @Override
    public Observable<List<HaoPray>> queryRecord(String id, int skip, int limit) {
        SQLiteHelper sqlite = SQLiteHelper.getInstance();
        List<HaoPray> results = sqlite.queryHaoPray(id, skip, limit);
        return Observable.just(results);
    }

    @Override
    public Observable<String> deletePrayRecord(HaoPray haoPray) {
        SQLiteDatabase database = SQLiteHelper.getInstance().getDatabase();
        if (TextUtils.isEmpty(haoPray.getObjectId())) {
            return Observable.just("empty objectId");
        }
        database.execSQL("delete from HaoPray where objectId=?", new Object[]{haoPray.getObjectId()});
        return Observable.just(haoPray.getObjectId());
    }

    @Override
    public Observable<Map<String, Integer>> groupById() {
        return SQLiteHelper.getInstance().queryIdCount("id");
    }

    @Override
    public Observable<String> addGroup(HaoPrayGroup newGroup) {
        return Observable.error(new Exception("功能开发中..."));
    }

    @Override
    public Observable<String> updateGroup(HaoPrayGroup newGroup) {
        int affects = SQLiteHelper.getInstance().updateHaoGroup(newGroup);
        if (affects > 0) {
            return Observable.just(newGroup.getObjectId());
        }
        return Observable.error(new Exception("affected row is 0"));
    }

    @Override
    public Observable<String> updateGroupWithSync(HaoPrayGroup newGroup, String oldPrayId) {
        return Observable.fromCallable(() -> {
            int affects = SQLiteHelper.getInstance().updateHaoGroupWithSync(newGroup, oldPrayId);
            if (affects > 0) {
                return newGroup.getObjectId();
            }
            throw new Exception("affected row is 0");
        });
    }

    @Override
    public Observable<SQLiteHelper.MergeResult> detectAndMergeAfterUpdate(HaoPrayGroup updatedGroup) {
        return Observable.fromCallable(() ->
            SQLiteHelper.getInstance().detectAndMergeAfterUpdate(updatedGroup)
        );
    }
}
