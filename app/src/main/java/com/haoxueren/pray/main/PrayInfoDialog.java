package com.haoxueren.pray.main;

import android.content.Context;
import android.widget.TextView;

import com.haoxueren.base.BaseDialog;
import com.haoxueren.pray.R;

import java.text.MessageFormat;

public class PrayInfoDialog extends BaseDialog {

    private TextView prayIdView;

    public PrayInfoDialog(Context context) {
        super(context);
    }

    @Override
    public int getLayoutResId() {
        return R.layout.dialog_pray_info;
    }

    @Override
    protected void bindView() {
        prayIdView = this.findViewById(R.id.prayIdView);
    }

    @Override
    protected void initView() {

    }

    public void initData(String prayId) {
        prayIdView.setText(MessageFormat.format("当前分组：{0}", prayId));
    }
}
