package com.haoxueren.pray.main;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.group.HaoPrayGroup;
import com.haoxueren.pray.service.DataService;
import com.haoxueren.pray.service.GroupService;
import com.haoxueren.pray.service.HaoPrayService;
import com.haoxueren.sqlite.SQLiteHelper;
import com.haoxueren.restful.GsonUtils;
import com.haoxueren.restful.QueryBuilder;
import com.haoxueren.utils.Base16;
import com.haoxueren.utils.DateUtils;
import com.haoxueren.utils.Singleton;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import io.reactivex.Observable;

/**
 * API交互模式
 */
public class ApiPresenter implements MainPresenter {

    /**
     * 更新标题(目标培育进度)
     */
    public Observable<String> updateTitle() {
        Observable<Integer> today = queryTodayIds().map(Set::size);
        Observable<Integer> total = DataService.getInstance().getGroup2PrayMap().map(map -> {
            Set<Integer> groupIds = map.keySet();
            Iterator<Integer> iterator = groupIds.iterator();
            while (iterator.hasNext()) {
                if (iterator.next() < 0) {
                    iterator.remove(); // 删除小于0的groupId
                }
            }
            return groupIds.size();
        });
        return today.zipWith(total, (count1, count2) ->
                String.format(Locale.CHINA, "目标培育计划(%d/%d)", count1, count2));
    }

    /**
     * 从今日未完成的分组中随机抽取一个分组id，再从分组中随机抽取一个目标id
     */
    public Observable<String> getRandomId() {
        DataService service = DataService.getInstance();
        return queryTodayIds()
                .flatMap(service::getGroupIdMap)
                .doOnNext(map -> { // 去除未启用的分组
                    Iterator<Map.Entry<Integer, Set<String>>> iterator = map.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Integer groupId = iterator.next().getKey();
                        if (groupId < 0) {
                            iterator.remove(); // 删除小于0的分组
                        }
                    }
                })
                .map(map -> {
                    if (map.isEmpty()) {
                        return "";
                    }
                    Random random = Singleton.getRandom();
                    Integer[] groupIds = map.keySet().toArray(new Integer[0]);
                    int groupIndex = random.nextInt(groupIds.length);
                    Integer groupId = groupIds[groupIndex];
                    Set<String> prayIdSet = map.get(groupId);
                    String[] prayIdArray = prayIdSet.toArray(new String[0]);
                    int dayOfYear = Calendar.getInstance().get(Calendar.DAY_OF_YEAR);
                    int prayIndex = dayOfYear % prayIdArray.length; // 按天轮流显示
                    return prayIdArray[prayIndex];
                });
    }

    /**
     * 请求HaoPrayGroup的所有记录
     */
    @Override
    public Observable<List<HaoPrayGroup>> queryGroup(int fromId) {
        QueryBuilder builder = QueryBuilder.newInstance()
                .greaterThanOrEqualTo("groupId", fromId)
                .order("groupId").limit(500);
        return GroupService.getInstance().query(builder).map(json -> {
            List<HaoPrayGroup> list = new ArrayList<>();
            JsonObject result = GsonUtils.toJsonObject(json);
            JsonArray array = result.getAsJsonArray("results");
            for (JsonElement element : array) {
                HaoPrayGroup bean = GsonUtils.getGson().fromJson(element, HaoPrayGroup.class);
                list.add(bean);
            }
            return list;
        });
    }

    /**
     * 查询今天已培育过的目标id
     */
    public Observable<Set<String>> queryTodayIds() {
        QueryBuilder builder = QueryBuilder.newInstance()
                .where("date", DateUtils.today())
                .groupBy("id");
        return HaoPrayService.getInstance().query(builder).map(json -> {
            JsonObject result = GsonUtils.toJsonObject(json);
            JsonArray array = result.getAsJsonArray("results");
            Set<String> todayIdSet = new HashSet<>();
            for (JsonElement element : array) {
                JsonObject item = element.getAsJsonObject();
                String id = item.get("id").getAsString();
                todayIdSet.add(id);
            }
            return todayIdSet;
        });
    }


    public Observable<List<HaoPray>> queryRecord(String id, int skip, int limit) {
        QueryBuilder builder = QueryBuilder.newInstance()
                .where("id", id)
                .skip(skip)
                .limit(limit)
                .order("-date");
        return HaoPrayService.getInstance().query(builder).map(json -> {
            Gson gson = GsonUtils.getGson();
            JsonObject result = gson.fromJson(json, JsonObject.class);
            JsonElement data = result.get("results");
            HaoPray[] haoPrayArray = gson.fromJson(data, HaoPray[].class);
            return Arrays.asList(haoPrayArray);
        });
    }

    public Observable<String> saveHaoPray(final HaoPray haoPray) {
        if (TextUtils.isEmpty(haoPray.getId())
                || TextUtils.isEmpty(haoPray.getDate())
                || TextUtils.isEmpty(haoPray.getPray())) {
            return Observable.error(new NullPointerException("必填字段不可为空"));
        }
        JsonObject jsonObject = haoPray.toJsonObject();
        return HaoPrayService.getInstance()
                .save(jsonObject)
                .map(json -> {
                    Gson gson = GsonUtils.getGson();
                    JsonObject result = gson.fromJson(json, JsonObject.class);
                    boolean hasCode = result.has("code");
                    if (hasCode) {
                        return result.get("error").getAsString();
                    }
                    return haoPray.getPray();
                });
    }

    /**
     * 保存到HaoPrayGroup
     */
    public Observable<String> saveHaoGroup(Integer groupId, String prayId, String pray) {
        JsonObject params = new JsonObject();
        params.addProperty("groupId", groupId);
        params.addProperty("prayId", prayId);
        params.addProperty("pray", Base16.encode(pray));
        return GroupService.getInstance().save(params);
    }

    public Observable<String> deletePrayRecord(HaoPray haoPray) {
        if (haoPray == null) {
            return Observable.never();
        }
        return HaoPrayService.getInstance().delete(haoPray.getObjectId());
    }

    @Override
    public Observable<Map<String, Integer>> groupById() {
        QueryBuilder builder = QueryBuilder.newInstance()
                .limit(500).groupBy("id").groupCount(true);
        return HaoPrayService.getInstance().query(builder).map(json -> {
            JsonObject result = GsonUtils.toJsonObject(json);
            JsonArray array = result.getAsJsonArray("results");
            Map<String, Integer> map = new HashMap<>();
            for (JsonElement element : array) {
                JsonObject item = element.getAsJsonObject();
                String id = item.get("id").getAsString();
                Integer count = item.get("_count").getAsInt();
                map.put(id, count);
            }
            return map;
        });
    }

    /**
     * 添加一个分组
     */
    public Observable<String> addGroup(HaoPrayGroup newGroup) {
        JsonObject params = newGroup.toJson();
        return GroupService.getInstance().save(params);
    }

    /**
     * 修改一个分组
     */
    public Observable<String> updateGroup(HaoPrayGroup newGroup) {
        JsonObject params = newGroup.toJson();
        return GroupService.getInstance().update(newGroup.getObjectId(), params);
    }

    /**
     * 修改一个分组，并同步更新关联的HaoPray表数据
     * 注意：API模式下的数据同步需要通过服务端实现，这里暂时使用普通更新
     */
    @Override
    public Observable<String> updateGroupWithSync(HaoPrayGroup newGroup, String oldPrayId) {
        // API模式下，数据同步应该由服务端处理
        // 这里先使用普通的更新方法，实际项目中应该调用专门的同步API
        return updateGroup(newGroup);
    }

    /**
     * 检测并合并重复记录
     * API模式下应该由服务端处理合并逻辑
     */
    @Override
    public Observable<SQLiteHelper.MergeResult> detectAndMergeAfterUpdate(HaoPrayGroup updatedGroup) {
        // API模式下应该调用服务端接口进行合并检测
        // 这里暂时返回无合并结果
        return Observable.just(new SQLiteHelper.MergeResult(false, 0, null));
    }
}
