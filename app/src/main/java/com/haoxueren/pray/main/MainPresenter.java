package com.haoxueren.pray.main;

import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.config.AppConfig;
import com.haoxueren.pray.config.Mode;
import com.haoxueren.pray.group.HaoPrayGroup;
import com.haoxueren.sqlite.SQLiteHelper;

import java.util.List;
import java.util.Map;
import java.util.Set;

import io.reactivex.Observable;

public interface MainPresenter {

    public static MainPresenter getInstance() {
        if (AppConfig.getMode() == Mode.API) {
            return new ApiPresenter();
        }
        return new SQLitePresenter();
    }

    public Observable<String> updateTitle();

    public Observable<String> getRandomId();

    public Observable<Set<String>> queryTodayIds();

    public Observable<String> saveHaoPray(final HaoPray haoPray);

    public Observable<String> saveHaoGroup(Integer groupId, String prayId, String pray);

    public Observable<List<HaoPrayGroup>> queryGroup(int fromId);

    public Observable<List<HaoPray>> queryRecord(String id, int skip, int limit);

    public Observable<String> deletePrayRecord(HaoPray haoPray);

    public Observable<Map<String, Integer>> groupById();

    /**
     * 添加一个分组
     */
    public Observable<String> addGroup(HaoPrayGroup newGroup);

    /**
     * 修改一个分组
     */
    public Observable<String> updateGroup(HaoPrayGroup newGroup);

    /**
     * 修改一个分组，并同步更新关联的HaoPray表数据
     */
    public Observable<String> updateGroupWithSync(HaoPrayGroup newGroup, String oldPrayId);

    /**
     * 检测并合并重复记录
     */
    public Observable<SQLiteHelper.MergeResult> detectAndMergeAfterUpdate(HaoPrayGroup updatedGroup);

}
