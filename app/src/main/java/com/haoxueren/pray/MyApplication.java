package com.haoxueren.pray;

import android.app.Application;
import android.content.Context;

import androidx.appcompat.app.AppCompatDelegate;

import com.haoxueren.helper.LifecycleObserver;
import com.haoxueren.pray.log.MyLog;
import com.haoxueren.sqlite.BackupHelper;
import com.haoxueren.sqlite.DeviceDiscoveryManager;
import com.haoxueren.sqlite.EnhancedSocketManager;
import com.haoxueren.utils.ContextManager;
import com.haoxueren.utils.ErrorUtils;

import io.reactivex.plugins.RxJavaPlugins;
import pl.com.salsoft.sqlitestudioremote.SQLiteStudioService;

public class MyApplication extends Application {

    private static Context context;

    @Override
    public void onCreate() {
        super.onCreate();
        context = getApplicationContext();
        ContextManager.initContext(this);
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM);
        LifecycleObserver.getInstance().register(this);
        RxJavaPlugins.setErrorHandler(Throwable::printStackTrace);
        SQLiteStudioService sqLiteStudioService = SQLiteStudioService.instance();
        sqLiteStudioService.setPort(2010);
        sqLiteStudioService.setPassword("haoxueren");
        sqLiteStudioService.start(context);
        
        // 应用启动时自动备份数据库
        BackupHelper.backupSQLite(this)
                .subscribe(file -> MyLog.info("数据库备份成功", file.getPath()), ErrorUtils::onError);

        // 启动网络服务，确保设备可以被发现和同步
        startNetworkServices();
    }

    public static Context getContext() {
        return context;
    }

    /**
     * 启动网络服务
     */
    private void startNetworkServices() {
        // 启动增强的Socket服务器用于数据库同步
        EnhancedSocketManager.startEnhancedServer(2024);

        // 启动UDP响应服务器用于设备发现
        DeviceDiscoveryManager.getInstance().startUdpResponseServer();

        MyLog.info("网络服务启动", "Socket服务器和UDP响应服务器已启动");
    }
}
