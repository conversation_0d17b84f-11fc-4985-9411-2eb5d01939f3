package com.haoxueren.pray;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import java.util.function.Consumer;

public class ScreenReceiver extends BroadcastReceiver {

    private Consumer<Intent> consumer;

    public void register(Context context) {
        IntentFilter filter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
        context.registerReceiver(this, filter);
    }

    @Override
    public void onReceive(Context context, Intent intent) {

    }

    public void onScreenOff(Consumer<Intent> consumer) {
        this.consumer = consumer;
    }

    public void unregister(Context context) {
        context.unregisterReceiver(this);
    }

}
