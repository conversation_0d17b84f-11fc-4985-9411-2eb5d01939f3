package com.haoxueren.pray.debug;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.haoxueren.pray.R;
import com.haoxueren.utils.NetworkDiscoveryDiagnostic;
import com.haoxueren.utils.ToastUtils;

import io.reactivex.disposables.CompositeDisposable;

/**
 * 网络诊断调试界面
 */
public class NetworkDiagnosticActivity extends AppCompatActivity {
    
    private EditText targetIpEditText;
    private Button diagnoseButton;
    private Button fixButton;
    private Button clearButton;
    private TextView resultTextView;
    private ScrollView scrollView;
    
    private CompositeDisposable compositeDisposable = new CompositeDisposable();
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_network_diagnostic);
        
        initViews();
        setupListeners();
    }
    
    private void initViews() {
        targetIpEditText = findViewById(R.id.et_target_ip);
        diagnoseButton = findViewById(R.id.btn_diagnose);
        fixButton = findViewById(R.id.btn_fix);
        clearButton = findViewById(R.id.btn_clear);
        resultTextView = findViewById(R.id.tv_result);
        scrollView = findViewById(R.id.scroll_view);
        
        // 预设IP地址
        targetIpEditText.setText("***********"); // 默认设置为设备A的IP
    }
    
    private void setupListeners() {
        diagnoseButton.setOnClickListener(v -> {
            String targetIp = targetIpEditText.getText().toString().trim();
            if (TextUtils.isEmpty(targetIp)) {
                ToastUtils.showToast("请输入目标设备IP地址");
                return;
            }
            
            diagnoseNetworkIssues(targetIp);
        });
        
        fixButton.setOnClickListener(v -> fixNetworkIssues());
        
        clearButton.setOnClickListener(v -> {
            resultTextView.setText("");
        });
    }
    
    private void diagnoseNetworkIssues(String targetIp) {
        diagnoseButton.setEnabled(false);
        appendResult("开始诊断网络发现问题...\n\n");
        
        compositeDisposable.add(
            NetworkDiscoveryDiagnostic.diagnoseNetworkDiscovery(targetIp)
                .subscribe(
                    result -> {
                        appendResult(result);
                        diagnoseButton.setEnabled(true);
                        scrollToBottom();
                    },
                    error -> {
                        appendResult("诊断失败: " + error.getMessage() + "\n");
                        diagnoseButton.setEnabled(true);
                        scrollToBottom();
                    }
                )
        );
    }
    
    private void fixNetworkIssues() {
        fixButton.setEnabled(false);
        appendResult("\n开始修复网络发现问题...\n\n");
        
        compositeDisposable.add(
            NetworkDiscoveryDiagnostic.fixNetworkDiscoveryIssues()
                .subscribe(
                    result -> {
                        appendResult(result);
                        fixButton.setEnabled(true);
                        scrollToBottom();
                    },
                    error -> {
                        appendResult("修复失败: " + error.getMessage() + "\n");
                        fixButton.setEnabled(true);
                        scrollToBottom();
                    }
                )
        );
    }
    
    private void appendResult(String text) {
        runOnUiThread(() -> {
            resultTextView.append(text);
        });
    }
    
    private void scrollToBottom() {
        runOnUiThread(() -> {
            scrollView.post(() -> scrollView.fullScroll(ScrollView.FOCUS_DOWN));
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        compositeDisposable.clear();
    }
}
