package com.haoxueren.pray.sync

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.haoxueren.pray.R
import com.haoxueren.sqlite.DatabaseSyncManager
import com.haoxueren.utils.DateUtils

/**
 * 设备同步列表适配器
 */
class DeviceSyncAdapter(
    private val devices: MutableList<DatabaseSyncManager.DeviceInfo>,
    private val onDeviceClick: (DatabaseSyncManager.DeviceInfo) -> Unit
) : RecyclerView.Adapter<DeviceSyncAdapter.DeviceViewHolder>() {

    private var selectedDevice: DatabaseSyncManager.DeviceInfo? = null

    class DeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val deviceIcon: ImageView = itemView.findViewById(R.id.deviceIcon)
        val deviceName: TextView = itemView.findViewById(R.id.deviceName)
        val deviceAddress: TextView = itemView.findViewById(R.id.deviceAddress)
        val deviceStatus: TextView = itemView.findViewById(R.id.deviceStatus)
        val deviceLastSeen: TextView = itemView.findViewById(R.id.deviceLastSeen)
        val selectionIndicator: View = itemView.findViewById(R.id.selectionIndicator)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_device_sync, parent, false)
        return DeviceViewHolder(view)
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        val device = devices[position]
        
        holder.deviceName.text = device.name
        holder.deviceAddress.text = "${device.ip}:${device.port}"
        
        // 设置设备状态
        if (device.isOnline) {
            holder.deviceStatus.text = "在线"
            holder.deviceStatus.setTextColor(
                holder.itemView.context.getColor(android.R.color.holo_green_dark)
            )
            holder.deviceIcon.setImageResource(R.drawable.ic_device_online)
        } else {
            holder.deviceStatus.text = "离线"
            holder.deviceStatus.setTextColor(
                holder.itemView.context.getColor(android.R.color.holo_red_dark)
            )
            holder.deviceIcon.setImageResource(R.drawable.ic_device_offline)
        }
        
        // 设置最后见到时间
        val lastSeenText = if (device.lastSeen > 0) {
            "最后见到: " + DateUtils.format(device.lastSeen, "MM-dd HH:mm")
        } else {
            "刚刚发现"
        }
        holder.deviceLastSeen.text = lastSeenText
        
        // 设置选中状态
        val isSelected = device == selectedDevice
        holder.selectionIndicator.visibility = if (isSelected) View.VISIBLE else View.GONE
        
        // 设置背景
        if (isSelected) {
            holder.itemView.setBackgroundResource(R.drawable.shape_selected_item)
        } else {
            holder.itemView.setBackgroundResource(R.drawable.shape_normal_item)
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener {
            onDeviceClick(device)
        }
        
        // 长按删除设备
        holder.itemView.setOnLongClickListener {
            showDeleteDeviceDialog(holder.itemView.context, device, position)
            true
        }
    }

    override fun getItemCount(): Int = devices.size

    fun setSelectedDevice(device: DatabaseSyncManager.DeviceInfo) {
        val oldSelected = selectedDevice
        selectedDevice = device
        
        // 刷新旧选中项
        oldSelected?.let { old ->
            val oldIndex = devices.indexOf(old)
            if (oldIndex >= 0) {
                notifyItemChanged(oldIndex)
            }
        }
        
        // 刷新新选中项
        val newIndex = devices.indexOf(device)
        if (newIndex >= 0) {
            notifyItemChanged(newIndex)
        }
    }

    fun getSelectedDevice(): DatabaseSyncManager.DeviceInfo? = selectedDevice

    private fun showDeleteDeviceDialog(
        context: android.content.Context, 
        device: DatabaseSyncManager.DeviceInfo, 
        position: Int
    ) {
        androidx.appcompat.app.AlertDialog.Builder(context)
            .setTitle("删除设备")
            .setMessage("确定要删除设备 \"${device.name}\" 吗？")
            .setPositiveButton("删除") { _, _ ->
                removeDevice(position)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun removeDevice(position: Int) {
        if (position >= 0 && position < devices.size) {
            val device = devices[position]
            
            // 如果删除的是当前选中设备，清除选中状态
            if (device == selectedDevice) {
                selectedDevice = null
            }
            
            devices.removeAt(position)
            notifyItemRemoved(position)
            
            // 从设备管理器中移除
            com.haoxueren.sqlite.DeviceDiscoveryManager.getInstance().removeDevice(device)
        }
    }

    fun updateDeviceStatus(device: DatabaseSyncManager.DeviceInfo, isOnline: Boolean) {
        val index = devices.indexOf(device)
        if (index >= 0) {
            device.isOnline = isOnline
            device.lastSeen = System.currentTimeMillis()
            notifyItemChanged(index)
        }
    }

    fun addDevice(device: DatabaseSyncManager.DeviceInfo) {
        // 检查是否已存在
        for (existing in devices) {
            if (existing.ip == device.ip && existing.port == device.port) {
                // 更新现有设备信息
                existing.name = device.name
                existing.isOnline = device.isOnline
                existing.lastSeen = device.lastSeen
                notifyItemChanged(devices.indexOf(existing))
                return
            }
        }
        
        // 添加新设备
        devices.add(device)
        notifyItemInserted(devices.size - 1)
    }

    fun clearDevices() {
        val size = devices.size
        devices.clear()
        selectedDevice = null
        notifyItemRangeRemoved(0, size)
    }

    fun refreshAllDevices() {
        notifyDataSetChanged()
    }
}
