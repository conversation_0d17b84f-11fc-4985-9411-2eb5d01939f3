package com.haoxueren.pray;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import androidx.core.hardware.fingerprint.FingerprintManagerCompat.AuthenticationResult;
import androidx.core.os.CancellationSignal;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.haoxueren.utils.LunarCalendar;

import java.util.function.Consumer;

public class FingerprintDialog extends Dialog {

    private Activity context;
    private ImageView beautyImageView;
    private TextView fingerprintTextView;

    private CancellationSignal signal;
    private Consumer<AuthenticationResult> authenticateSuccess;

    public FingerprintDialog(Context context) {
        super(context);
        this.context = (Activity) context;
        this.setContentView(R.layout.dialog_fingerprint);
        WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
        layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        getWindow().setAttributes(layoutParams);
        beautyImageView = findViewById(R.id.beautyImageView);
        fingerprintTextView = findViewById(R.id.fingerprintTextView);
        fingerprintTextView.setText(LunarCalendar.today());
        this.setCanceledOnTouchOutside(false);
        this.setOnShowListener(this::onShow);
        this.setOnCancelListener(this::onDialogCancel);
        RandomImage.loadImage(this.getContext()).into(beautyImageView);
    }

    public void onAuthenticateSuccess(Consumer<AuthenticationResult> consumer) {
        this.authenticateSuccess = consumer;
    }

    public void onShow(DialogInterface dialog) {
        FingerprintManagerCompat manager = FingerprintManagerCompat.from(context);
        // 硬件是否支持指纹识别
        boolean isDetected = manager.isHardwareDetected();
        // 是否已经有录入的指纹
        boolean hasFingerprints = manager.hasEnrolledFingerprints();
        if (isDetected && hasFingerprints) {
            signal = new CancellationSignal();
            manager.authenticate(null, 0, signal, new FingerprintManagerCompat.AuthenticationCallback() {
                @Override
                public void onAuthenticationSucceeded(AuthenticationResult result) {
                    super.onAuthenticationSucceeded(result);
                    authenticateSuccess.accept(result);
                    FingerprintDialog.this.dismiss();
                }

                @Override
                public void onAuthenticationFailed() {
                    super.onAuthenticationFailed();
                    fingerprintTextView.setText("指纹验证失败");
                    fingerprintTextView.setTextColor(Color.RED);
                }
            }, null);
        }
    }

    /**
     * 对话框被取消时调用
     */
    public void onDialogCancel(DialogInterface dialog) {
        if (signal != null) {
            signal.cancel();
        }
        context.finish();
    }


}
