package com.haoxueren.pray.bean;

import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.haoxueren.utils.Base16;
import com.haoxueren.utils.DateUtils;
import com.haoxueren.utils.DigestUtils;

public class Hao<PERSON>ray implements java.io.Serializable {

    private String id = "";
    private String date = "";
    private Integer count = 0;
    private String pray = "";
    
    // BmobObject 中的核心字段
    private String objectId;
    private String createdAt;
    private String updatedAt;
    private String _c_;
    
    public HaoPray() {
        this._c_ = this.getClass().getSimpleName();
    }

    public String getObjectId() {
        if (!TextUtils.isEmpty(objectId)) {
            return objectId;
        }
        try {
            return DigestUtils.md5(System.currentTimeMillis() + id).substring(0, 10);
        } catch (Exception e) {
            return System.currentTimeMillis() + "".substring(0, 10);
        }
    }
    
    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getCreatedAt() {
        if (TextUtils.isEmpty(createdAt)) {
            return DateUtils.today("yyyy-MM-dd HH:mm:ss");
        }
        return createdAt;
    }
    
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        if (TextUtils.isEmpty(updatedAt)) {
            return DateUtils.today("yyyy-MM-dd HH:mm:ss");
        }
        return updatedAt;
    }
    
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getTableName() {
        return this._c_;
    }
    
    public void setTableName(String tableName) {
        this._c_ = tableName;
    }

    public JsonObject toJsonObject() {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("id", id);
        jsonObject.addProperty("date", date);
        jsonObject.addProperty("count", count);
        jsonObject.addProperty("pray", pray);
        return jsonObject;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getCount() {
        return count;
    }

    public String getPray() {
        if (Base16.isBase16(pray)) {
            String decode = Base16.decode(pray);
            return decode;
        }
        return pray;
    }

    public String getEncodePray() {
        if (Base16.isBase16(pray)) {
            return pray;
        }
        return Base16.encode(pray);
    }

    public void setPray(String pray) {
        if (Base16.isBase16(pray)) {
            this.pray = pray;
        } else {
            this.pray = Base16.encode(pray);
        }
    }

    public void setPray(String pray, boolean encode) {
        this.pray = encode ? Base16.encode(pray) : pray;
    }

    public void setCount(String count) {
        this.count = Integer.parseInt(count);
    }
}
