package com.haoxueren.pray.group;

/**
 * 分组数据验证工具类
 * 用于验证HaoPrayGroup相关数据的有效性
 */
public class GroupDataValidator {

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        public final boolean isValid;
        public final String errorMessage;

        public ValidationResult(boolean isValid, String errorMessage) {
            this.isValid = isValid;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }

        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }
    }

    /**
     * 验证GroupId的有效性
     */
    public static ValidationResult validateGroupId(String groupIdStr) {
        if (isEmpty(groupIdStr)) {
            return ValidationResult.error("GroupId不能为空");
        }

        try {
            int groupId = Integer.parseInt(groupIdStr);
            if (groupId < -999 || groupId > 9999) {
                return ValidationResult.error("GroupId必须在-999到9999范围之间");
            }
            return ValidationResult.success();
        } catch (NumberFormatException e) {
            return ValidationResult.error("GroupId必须是有效的整数");
        }
    }

    /**
     * 验证PrayId的有效性
     */
    public static ValidationResult validatePrayId(String prayId) {
        if (isEmpty(prayId)) {
            return ValidationResult.error("PrayId不能为空");
        }

        // 检查长度是否为12位
        if (prayId.length() != 12) {
            return ValidationResult.error("PrayId必须是12位数字");
        }

        // 检查是否只包含数字字符
        if (!prayId.matches("^[0-9]{12}$")) {
            return ValidationResult.error("PrayId必须是12位数字");
        }

        return ValidationResult.success();
    }

    /**
     * 验证祈祷内容的有效性
     */
    public static ValidationResult validatePrayContent(String prayContent) {
        // 祈祷内容可以为空
        if (isEmpty(prayContent)) {
            return ValidationResult.success();
        }

        if (prayContent.length() > 1000) {
            return ValidationResult.error("祈祷内容长度不能超过1000个字符");
        }

        return ValidationResult.success();
    }

    /**
     * 验证完整的HaoPrayGroup数据
     */
    public static ValidationResult validateHaoPrayGroup(String groupIdStr, String prayId, String prayContent) {
        // 验证GroupId
        ValidationResult groupIdResult = validateGroupId(groupIdStr);
        if (!groupIdResult.isValid) {
            return groupIdResult;
        }

        // 验证PrayId
        ValidationResult prayIdResult = validatePrayId(prayId);
        if (!prayIdResult.isValid) {
            return prayIdResult;
        }

        // 验证祈祷内容
        ValidationResult prayContentResult = validatePrayContent(prayContent);
        if (!prayContentResult.isValid) {
            return prayContentResult;
        }

        return ValidationResult.success();
    }



    /**
     * 检查GroupId是否为有效的分组ID
     */
    public static boolean isValidGroupIdRange(int groupId) {
        return groupId >= -999 && groupId <= 9999;
    }

    /**
     * 检查是否为系统保留的GroupId
     */
    public static boolean isSystemReservedGroupId(int groupId) {
        // 负数GroupId通常用于系统保留
        return groupId < 0;
    }

    /**
     * 检查字符串是否为空或null
     * 替代Android的TextUtils.isEmpty()方法，用于单元测试兼容性
     */
    private static boolean isEmpty(String str) {
        return str == null || str.length() == 0;
    }
}
