package com.haoxueren.pray.group;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.Button;
import android.widget.TextView;

import com.haoxueren.base.BaseDialog;
import com.haoxueren.pray.R;
import com.haoxueren.pray.main.MainPresenter;
import com.haoxueren.utils.DateUtils;
import com.haoxueren.utils.ToastUtils;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

import io.reactivex.Observable;

@SuppressLint("CheckResult")
public class UpdateGroupDialog extends BaseDialog {

    public static void show(Context activity, HaoPrayGroup group, Consumer<HaoPrayGroup> result) {
        UpdateGroupDialog dialog = new UpdateGroupDialog(activity);
        dialog.initData(group, result);
        dialog.show();
    }

    private TextView groupIdEditText;
    private TextView prayIdEditText;
    private TextView groupTextEditText;
    private Button updateButton;
    private Button detailButton;

    public UpdateGroupDialog(Context context) {
        super(context);
    }

    @Override
    public int getLayoutResId() {
        return R.layout.dialog_update_group;
    }

    @Override
    protected void bindView() {
        groupIdEditText = this.findViewById(R.id.groupIdEditText);
        prayIdEditText = this.findViewById(R.id.prayIdEditText);
        groupTextEditText = this.findViewById(R.id.groupTextEditText);
        updateButton = this.findViewById(R.id.updateButton);
        detailButton = this.findViewById(R.id.detailButton);
    }

    @Override
    protected void initView() {

    }

    private void initData(HaoPrayGroup group, Consumer<HaoPrayGroup> result) {
        boolean isAddGroup = (group == null);
        if (isAddGroup) {
            String prayId = DateUtils.today("yyyyMMddHHmm");
            group = new HaoPrayGroup(0, prayId, "");
        }
        String originalPrayId = group.prayId; // 保存原始prayId用于数据同步
        String objectId = group.getObjectId();
        groupIdEditText.setText(group.groupId + "");
        prayIdEditText.setText(originalPrayId);
        groupTextEditText.setText(group.getPray());

        // 设置详情按钮点击事件
        detailButton.setOnClickListener(v -> {
            String currentPrayId = prayIdEditText.getText().toString().trim();
            if (!TextUtils.isEmpty(currentPrayId)) {
                PrayRecordDetailActivity.start(getContext(), currentPrayId);
            } else {
                ToastUtils.showToast("祈祷ID不能为空");
            }
        });

        updateButton.setOnClickListener(v -> {
            v.setEnabled(false);
            String newGroupId = groupIdEditText.getText().toString().trim();
            String newPrayId = prayIdEditText.getText().toString().trim();
            String newGroupText = groupTextEditText.getText().toString().trim();

            // 使用数据验证工具进行完整验证
            GroupDataValidator.ValidationResult validationResult =
                GroupDataValidator.validateHaoPrayGroup(newGroupId, newPrayId, newGroupText);

            if (!validationResult.isValid) {
                v.setEnabled(true);
                ToastUtils.showToast(validationResult.errorMessage);
                return;
            }

            HaoPrayGroup newGroup = new HaoPrayGroup(Integer.parseInt(newGroupId), newPrayId, newGroupText);
            Observable<String> groupService;
            if (isAddGroup) {
                groupService = MainPresenter.getInstance().addGroup(newGroup);
            } else {
                newGroup.setObjectId(objectId);
                // 检查prayId是否发生变化，如果变化则使用同步更新方法
                if (!originalPrayId.equals(newPrayId)) {
                    groupService = MainPresenter.getInstance().updateGroupWithSync(newGroup, originalPrayId);
                } else {
                    groupService = MainPresenter.getInstance().updateGroup(newGroup);
                }
            }
            groupService.subscribe(json -> {
                // 更新成功后，检测并合并重复记录
                if (!isAddGroup) {
                    MainPresenter.getInstance().detectAndMergeAfterUpdate(newGroup)
                        .subscribe(mergeResult -> {
                            result.accept(newGroup);
                            this.dismiss();

                            if (mergeResult.hasMerged) {
                                ToastUtils.showToast("更新成功 - " + mergeResult.message);
                            } else {
                                ToastUtils.showToast("更新成功");
                            }
                        }, mergeError -> {
                            // 即使合并失败，更新操作已经成功，只是提示合并错误
                            result.accept(newGroup);
                            this.dismiss();
                            ToastUtils.showToast("更新成功，但合并检测失败: " + mergeError.getMessage());
                        });
                } else {
                    // 新增记录不需要合并检测
                    result.accept(newGroup);
                    this.dismiss();
                    ToastUtils.showToast("添加成功");
                }
            }, e -> {
                v.setEnabled(true);
                ToastUtils.showToast("更新失败: " + e.getMessage());
            });
        });
    }


}
