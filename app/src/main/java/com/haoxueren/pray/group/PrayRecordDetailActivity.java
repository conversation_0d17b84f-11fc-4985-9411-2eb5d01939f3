package com.haoxueren.pray.group;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.haoxueren.base.BaseActivity;
import com.haoxueren.pray.R;
import com.haoxueren.widget.PrayRecordWidget;

/**
 * 祈祷记录详情页面
 * 显示指定 prayId 的所有祈祷记录
 */
public class PrayRecordDetailActivity extends BaseActivity {

    private static final String KEY_PRAY_ID = "pray_id";

    private Toolbar toolbar;
    private TextView titleTextView;
    private PrayRecordWidget recordWidget;
    
    private String prayId;

    /**
     * 启动详情页面
     * @param context 上下文
     * @param prayId 祈祷ID
     */
    public static void start(Context context, String prayId) {
        Intent intent = new Intent(context, PrayRecordDetailActivity.class);
        intent.putExtra(KEY_PRAY_ID, prayId);
        context.startActivity(intent);
    }

    @Override
    public int getLayoutResId() {
        return R.layout.activity_pray_record_detail;
    }

    @Override
    protected void bindView(View layout) {
        toolbar = findViewById(R.id.toolbar);
        titleTextView = findViewById(R.id.titleTextView);
        recordWidget = findViewById(R.id.recordWidget);
    }

    @Override
    protected void initView() {
        // 设置工具栏标题和返回按钮
        toolbar.setTitle("祈祷记录");
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    @Override
    protected void initData() {
        // 获取传递的 prayId
        Intent intent = getIntent();
        if (intent != null) {
            prayId = intent.getStringExtra(KEY_PRAY_ID);
        }
        
        if (TextUtils.isEmpty(prayId)) {
            finish();
            return;
        }
        
        // 设置详细标题信息
        titleTextView.setText(String.format("分组ID：%s", prayId));

        // 加载祈祷记录
        recordWidget.loadRecord(prayId, 0);
    }
}
