package com.haoxueren.pray.group;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.DiffUtil;

import com.haoxueren.base.BaseActivity;
import com.haoxueren.pray.R;
import com.haoxueren.pray.main.MainPresenter;
import com.haoxueren.pray.service.DataService;
import com.haoxueren.proxy.RecyclerViewProxy;
import com.haoxueren.proxy.SuperViewHolder;

import java.text.MessageFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 管理工具界面
 */
@SuppressLint("CheckResult")
public class AllGroupActivity extends BaseActivity {

    private static final String KEY_PRAY_ID = "current_pray_id";

    private Toolbar toolbar;
    private TextView prayIdView;
    private SwipeRefreshLayout refreshLayout;
    private RecyclerViewProxy<HaoPrayGroup> recyclerView;

    private List<HaoPrayGroup> groupList;
    private boolean pullRefreshLoadAll = true; // 控制下拉刷新加载模式，true: >=0，false: >0

    public static void start(Context context, String currentId) {
        Intent intent = new Intent(context, AllGroupActivity.class);
        intent.putExtra(KEY_PRAY_ID, currentId);
        context.startActivity(intent);
    }

    @Override
    public int getLayoutResId() {
        return R.layout.activity_all_group;
    }

    @Override
    protected void bindView(View layout) {
        toolbar = layout.findViewById(R.id.toolbar);
        prayIdView = layout.findViewById(R.id.prayIdView);
        refreshLayout = layout.findViewById(R.id.refreshLayout);
        recyclerView = new RecyclerViewProxy<HaoPrayGroup>(layout, R.id.recyclerView) {
            @Override
            protected SuperViewHolder<HaoPrayGroup> onCreateHolder(ViewGroup parent, int viewType) {
                return new HaoGroupHolder(parent);
            }
        };
    }

    @Override
    protected void initView() {
        refreshLayout.setRefreshing(true);
        refreshLayout.setOnRefreshListener(() -> {
            if (pullRefreshLoadAll) {
                refresh(0); // 加载 groupId >= 0
            } else {
                refresh(1); // 加载 groupId > 0
            }
            pullRefreshLoadAll = !pullRefreshLoadAll; // 每次切换
        });
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    @Override
    protected void initData() {
        refresh(1); // 普通加载只加载 groupId > 0
    }

    private void refresh(int fromId) {
        String prayId = getIntent().getStringExtra(KEY_PRAY_ID);
        if (TextUtils.isEmpty(prayId)) {
            prayIdView.setVisibility(View.GONE);
        }
        
        // 分离UI更新和数据查询操作
        DataService.getInstance().getGroupCount(groupId -> groupId >= fromId)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(count -> {
                    toolbar.setSubtitle(MessageFormat.format("所有分组({0})", count));
                });
                
        DataService.getInstance().getGroupId(prayId)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(groupId -> {
                    prayIdView.setText(MessageFormat.format("[ GroupId = {0} , PrayId = {1} ] ", groupId, prayId));
                });
                
        // 在后台线程处理数据排序
        MainPresenter.getInstance().queryGroup(fromId)
                .subscribeOn(Schedulers.io())
                .map(result -> {
                    // 在后台线程排序
                    Collections.sort(result, (g1, g2) -> {
                        if (g1.groupId.equals(g2.groupId)) {
                            return Integer.compare(g1.getPray().length(), g2.getPray().length());
                        }
                        return Integer.compare(g1.groupId, g2.groupId);
                    });
                    return result;
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result -> {
                    if (this.groupList == null) {
                        this.groupList = result;
                        recyclerView.setAdapter(result);
                        refreshLayout.setRefreshing(false); // 在首次加载完成时停止刷新动画
                    } else {
                        List<HaoPrayGroup> oldList = this.groupList;
                        List<HaoPrayGroup> newList = result;
                        
                        // 使用后台线程计算差异
                        Observable.fromCallable(() -> DiffUtil.calculateDiff(new DiffUtil.Callback() {
                            @Override
                            public int getOldListSize() {
                                return oldList.size();
                            }
                            @Override
                            public int getNewListSize() {
                                return newList.size();
                            }
                            @Override
                            public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
                                HaoPrayGroup oldItem = oldList.get(oldItemPosition);
                                HaoPrayGroup newItem = newList.get(newItemPosition);
                                return oldItem.groupId.equals(newItem.groupId) && oldItem.prayId.equals(newItem.prayId);
                            }
                            @Override
                            public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
                                HaoPrayGroup oldItem = oldList.get(oldItemPosition);
                                HaoPrayGroup newItem = newList.get(newItemPosition);
                                return oldItem.getPray().equals(newItem.getPray());
                            }
                        }))
                        .subscribeOn(Schedulers.computation())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(diffResult -> {
                            // 避免不必要的操作
                            this.groupList = newList;
                            recyclerView.getAdapterList().clear();
                            recyclerView.getAdapterList().addAll(newList);
                            // 只使用 DiffUtil 的差异更新，不再调用 notifyDataSetChanged
                            diffResult.dispatchUpdatesTo(recyclerView.getAdapter());
                            refreshLayout.setRefreshing(false);
                        }, throwable -> {
                            // 发生错误时也要停止刷新动画
                            refreshLayout.setRefreshing(false);
                        });
                    }
                });
    }

    private Observable<Integer> findGroupPosition(List<HaoPrayGroup> list, String prayId) {
        Observable<Integer> groupIdObservable;
        if (TextUtils.isEmpty(prayId)) {
            groupIdObservable = Observable.just(0);
        } else {
            groupIdObservable = DataService.getInstance().getGroupId(prayId);
        }
        return groupIdObservable.map(groupId -> {
            for (int i = 0; i < list.size(); i++) {
                if ((Objects.equals(list.get(i).groupId, groupId))) {
                    return i;
                }
            }
            return 0;
        });
    }

    private class HaoGroupHolder extends SuperViewHolder<HaoPrayGroup> {

        private TextView prayTextView, groupIdView;

        public HaoGroupHolder(ViewGroup parent) {
            super(parent, R.layout.item_pray_group);
        }

        @Override
        public void initView(View layout) {
            groupIdView = itemView.findViewById(R.id.groupIdView);
            prayTextView = itemView.findViewById(R.id.prayTextView);
        }

        @Override
        public void updateItem(HaoPrayGroup bean) {
            groupIdView.setText(MessageFormat.format("{0,number,000}.", bean.groupId));
            prayTextView.setText(TextUtils.isEmpty(bean.getPray()) ? bean.prayId : bean.getPray());

            // 为整个条目设置长按监听器
            View.OnLongClickListener longClickListener = v -> {
                BottomSheetUpdateGroupDialog.show(itemView.getContext(), bean, group -> {
                    // 清除DataService缓存，确保数据一致性
                    DataService.getInstance().clear();

                    // 重新加载数据以反映可能的合并结果
                    // 使用延迟加载确保数据库操作完成
                    Observable.timer(100, TimeUnit.MILLISECONDS)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(ignored -> {
                            // 重新查询数据
                            MainPresenter.getInstance().queryGroup(0)
                                .subscribe(newGroupList -> {
                                    groupList.clear();
                                    groupList.addAll(newGroupList);

                                    // 排序
                                    Collections.sort(groupList, (g1, g2) -> {
                                        if (g1.groupId.equals(g2.groupId)) {
                                            return Integer.compare(g1.getPray().length(), g2.getPray().length());
                                        }
                                        return Integer.compare(g1.groupId, g2.groupId);
                                    });

                                    recyclerView.notifyDataSetChanged();
                                }, error -> {
                                    // 如果重新加载失败，至少更新当前记录
                                    bean.groupId = group.groupId;
                                    bean.prayId = group.prayId;
                                    bean.setPray(group.getPray());
                                    recyclerView.notifyDataSetChanged();
                                });
                        });
                });
                return true;
            };

            // 为groupIdView和prayTextView都设置长按监听器
            groupIdView.setOnLongClickListener(longClickListener);
            prayTextView.setOnLongClickListener(longClickListener);
            itemView.setOnLongClickListener(longClickListener);
        }
    }
}