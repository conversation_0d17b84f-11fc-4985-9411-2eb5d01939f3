package com.haoxueren.pray.group;

import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.haoxueren.utils.Base16;
import com.haoxueren.utils.DateUtils;

public class HaoPrayGroup implements java.io.Serializable {

    public Integer groupId;
    public String prayId;
    private String pray;
    
    // BmobObject 中的核心字段
    private String objectId;
    private String createdAt;
    private String updatedAt;
    private String _c_;

    public HaoPrayGroup() {
        this._c_ = this.getClass().getSimpleName();
    }
    
    public HaoPrayGroup(Integer groupId, String prayId, String prayText) {
        this();
        if (groupId != null) {
            this.groupId = groupId;
        }
        if (!TextUtils.isEmpty(prayId)) {
            this.prayId = prayId;
        }
        if (!TextUtils.isEmpty(prayText)) {
            this.pray = Base16.isBase16(prayText) ? prayText : Base16.encode(prayText);
        }
    }
    
    public String getObjectId() {
        return objectId;
    }
    
    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedAt() {
        if (TextUtils.isEmpty(createdAt)) {
            return DateUtils.today("yyyy-MM-dd HH:mm:ss");
        }
        return createdAt;
    }

    public String getUpdatedAt() {
        if (TextUtils.isEmpty(updatedAt)) {
            return DateUtils.today("yyyy-MM-dd HH:mm:ss");
        }
        return updatedAt;
    }
    
    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getTableName() {
        return this._c_;
    }
    
    public void setTableName(String tableName) {
        this._c_ = tableName;
    }

    public String getPray() {
        if (Base16.isBase16(pray)) {
            return Base16.decode(pray);
        }
        return pray;
    }

    public void setPray(String pray) {
        this.pray = Base16.encode(pray);
    }

    public JsonObject toJson() {
        JsonObject params = new JsonObject();
        if (groupId != null) {
            params.addProperty("groupId", groupId);
        }
        if (!TextUtils.isEmpty(prayId)) {
            params.addProperty("prayId", prayId);
        }
        if (!TextUtils.isEmpty(pray)) {
            params.addProperty("pray", pray);
        }
        return params;
    }
}
