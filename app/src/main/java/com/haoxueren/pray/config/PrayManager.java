package com.haoxueren.pray.config;

import com.haoxueren.utils.ArrayUtils;
import com.haoxueren.utils.Singleton;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Random;

public class PrayManager {

    public static final String[] ARRAY_ID_PAUSED = new String[]{
            "201908200746", // 我会在2020年找到女朋友。
            "202012011914", // 我有一份稳步增长的副业收入。
            "202012101322", // 我会在2021年找到月薪30K的工作。
            "202004091005", // 我会顺利脱单。
            "201908260749", // 我会顺利长到***以上
            "201908251027", // 如何让对方爱上自己？
            "202002281630", // 每天花时间谈恋爱
            "201908010918", // 谈恋爱最重要的是什么？
            "201906232359", // 我个人资产100BTC以上
            "201909081557", // 今年最重要的事情是什么
            "202009140949", // 每天写一篇自定义控件的文章。
            // "202005251930", // 提升自己的技术影响力；扩大自己的人际交往圈。
            "201908202325", // 每天写一篇博客。
            "201906231138", // 我个人资产100万元以上。
            "202101011244", // 我个人资产500万元以上。
            "202011281019", // 亲密关系最重要的是什么？
            "202004241458", // 我会顺利解决面部皮肤问题。
            "202010251759", // 每天想象10年后的生活。
            "202005032014", // 改掉 秒回消息/抖腿乱晃/急于求成 的坏毛病。
    };


    public static final String[][] ARRAY_ID_RESUME = {
            {
                    "202101292138", // 遇到问题不要有焦虑情绪；遇到问题不要有畏难情绪。
                    "201908070803", // 任何问题都有相应的解决方案；任何目标都有相应的实现方法。
                    // 不要被想象中的困难吓倒；
            },
            {
                    // 要善用思维导图解决问题。
                    "202012091918", // 每天为目标努力1小时。
                    "201910150748", // 按事情的轻重程度去做；我精通自动化和批处理；
            },
    };

    /**
     * 每组选出一个，其余的暂停
     */
    public static String[] getPausedIds(String[] array) {
        Random random = Singleton.getRandom();
        List<String> pausedIdList = new LinkedList<>();
        for (String[] resumeIds : ARRAY_ID_RESUME) {
            // 如果两个数组有交集则略过
            if (ArrayUtils.hasCommonElement(array, resumeIds)) {
                continue;
            }
            // 当前组小一个目标时略过
            if (resumeIds.length <= 1) {
                continue;
            }
            int index = random.nextInt(resumeIds.length);
            for (int i = 0; i < resumeIds.length; i++) {
                if (i != index) {
                    pausedIdList.add(resumeIds[i]);
                }
            }
        }
        Collections.addAll(pausedIdList, ARRAY_ID_PAUSED);
        return pausedIdList.toArray(new String[0]);
    }

}
