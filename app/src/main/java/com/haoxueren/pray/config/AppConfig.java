package com.haoxueren.pray.config;

public interface AppConfig {

    public static AppConfig getInstance() {
        return new HaoConfig();
    }

    public static Mode getMode() {
        // boolean apiMode = MyPreferences.getInstance().getApiMode();
        // return apiMode ? Mode.API : Mode.SQLITE;
        return Mode.SQLITE;
    }

    public String getAppKey();

    public String getApiKey();

    public String getTableName();

}
