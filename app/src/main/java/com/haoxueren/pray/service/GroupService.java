package com.haoxueren.pray.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.haoxueren.pray.config.AppConfig;
import com.haoxueren.restful.BmobService;
import com.haoxueren.restful.GsonUtils;
import com.haoxueren.restful.QueryBuilder;

import io.reactivex.Observable;

public class GroupService extends BmobService {

    private static final GroupService ourInstance = new GroupService();

    public static GroupService getInstance() {
        return ourInstance;
    }

    private GroupService() {
    }

    @Override
    public String getApplicationId() {
        return AppConfig.getInstance().getAppKey();
    }

    @Override
    public String getRestApiKey() {
        return AppConfig.getInstance().getApiKey();
    }

    @Override
    public String getTableName() {
        return "HaoPrayGroup";
    }

    @Override
    public String getJsonBody(JsonObject bean) {
        return bean.toString();
    }

    public Observable<String> query(QueryBuilder query) {
        return super.query(query.build());
    }

    public Observable<Integer> count(QueryBuilder builder) {
        String condition = builder
                .count(true)
                .limit(0)
                .build();
        return super.query(condition)
                .map(json -> {
                    Gson gson = GsonUtils.getGson();
                    JsonObject result = gson.fromJson(json, JsonObject.class);
                    return result.get("count").getAsInt();
                });
    }
}
