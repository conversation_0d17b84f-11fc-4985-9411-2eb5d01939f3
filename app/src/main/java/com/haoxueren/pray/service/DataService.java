package com.haoxueren.pray.service;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import com.haoxueren.pray.group.HaoPrayGroup;
import com.haoxueren.pray.main.MainPresenter;
import com.haoxueren.utils.ArrayUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

import io.reactivex.Observable;

@SuppressLint("CheckResult")
public class DataService {

    private static final DataService instance = new DataService();

    private final Map<String, Integer> pray2GroupMap = new HashMap<>();

    private final Map<Integer, Set<String>> group2PrayMap = new HashMap<>();

    private final Map<String, Integer> id2CountMap = new HashMap<>();

    private DataService() {
    }

    public static DataService getInstance() {
        return instance;
    }

    public void clear() {
        group2PrayMap.clear();
        pray2GroupMap.clear();
        id2CountMap.clear();
    }

    /**
     * 根据prayId获取对应的groupId
     */
    public Observable<Integer> getGroupId(String prayId) {
        if (TextUtils.isEmpty(prayId)) {
            return Observable.never();
        }
        return getPray2GroupMap().map(map -> map.get(prayId));
    }

    /**
     * 查询所有目标及分组
     */
    public Observable<Map<String, Integer>> getPray2GroupMap() {
        if (pray2GroupMap.isEmpty()) {
            return MainPresenter.getInstance().queryGroup(0).map(this::parseToPrayIdMap);
        }
        return Observable.just(new HashMap<>(pray2GroupMap)); // 返回数据的副本
    }

    /**
     * 查询所有目标及分组
     */
    public Observable<Map<Integer, Set<String>>> getGroup2PrayMap() {
        if (group2PrayMap.isEmpty()) {
            return MainPresenter.getInstance().queryGroup(0).map(this::parseToGroupIdMap);
        }
        return Observable.just(new HashMap<>(group2PrayMap)); // 返回数据的副本
    }

    /**
     * 获取满足条件的分组数据
     */
    public Observable<Integer> getGroupCount(Predicate<Integer> groupId) {
        return this.getGroup2PrayMap().map(map -> {
            int count = 0;
            for (Integer id : map.keySet()) {
                if (groupId.test(id)) {
                    count++;
                }
            }
            return count;
        });
    }

    public Observable<Map<Integer, Set<String>>> getGroupIdMap(Set<String> todayIds) {
        Observable<Map<Integer, Set<String>>> observable;
        if (group2PrayMap.isEmpty()) {
            observable = MainPresenter.getInstance().queryGroup(0)
                    .map(this::parseToGroupIdMap);
        } else {
            observable = Observable.just(new HashMap<>(group2PrayMap));
        }
        return observable.doOnNext(map -> {
            Iterator<Map.Entry<Integer, Set<String>>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Set<String> prayIdSet = iterator.next().getValue();
                if (ArrayUtils.hasCommonElement(prayIdSet, todayIds)) {
                    iterator.remove();
                }
            }
        });
    }


    /**
     * 查询所有id及对应的count
     */
    public Observable<Map<String, Integer>> getId2CountMap() {
        if (id2CountMap.isEmpty()) {
            return MainPresenter.getInstance().groupById();
        } else {
            return Observable.just(new HashMap<>(id2CountMap));
        }
    }

    /**
     * 将数据填充到prayIdMap
     */
    private Map<String, Integer> parseToPrayIdMap(List<HaoPrayGroup> groupList) {
        for (HaoPrayGroup group : groupList) {
            pray2GroupMap.put(group.prayId, group.groupId);
        }
        return new HashMap<>(pray2GroupMap);
    }

    /**
     * 将数据填充到groupIdMap
     */
    private Map<Integer, Set<String>> parseToGroupIdMap(List<HaoPrayGroup> groupList) {
        for (HaoPrayGroup group : groupList) {
            Set<String> prayIdSet = group2PrayMap.get(group.groupId);
            if (prayIdSet == null) {
                prayIdSet = new HashSet<>();
                group2PrayMap.put(group.groupId, prayIdSet);
            }
            prayIdSet.add(group.prayId);
        }
        return new HashMap<>(group2PrayMap);
    }

}
