package com.haoxueren.pray.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.haoxueren.pray.config.AppConfig;
import com.haoxueren.restful.BmobService;
import com.haoxueren.restful.GsonUtils;
import com.haoxueren.restful.QueryBuilder;

import io.reactivex.Observable;

public class HaoPrayService extends BmobService {

    private AppConfig appConfig; // 数据库配置

    private HaoPrayService(AppConfig config) {
        this.appConfig = config;
    }

    public static HaoPrayService getInstance() {
        return new HaoPrayService(AppConfig.getInstance());
    }

    public static HaoPrayService getInstance(AppConfig config) {
        return new HaoPrayService(config);
    }

    @Override
    public String getApplicationId() {
        return appConfig.getAppKey();
    }

    @Override
    public String getRestApiKey() {
        return appConfig.getApiKey();
    }

    @Override
    public String getTableName() {
        return appConfig.getTableName();
    }

    @Override
    public String getJsonBody(JsonObject bean) {
        return bean.toString();
    }


    public Observable<String> query(QueryBuilder query) {
        return super.query(query.limit(500).build());
    }

    public Observable<Integer> count(QueryBuilder builder) {
        String condition = builder
                .count(true)
                .limit(0)
                .build();
        return super.query(condition)
                .map(json -> {
                    Gson gson = GsonUtils.getGson();
                    JsonObject result = gson.fromJson(json, JsonObject.class);
                    return result.get("count").getAsInt();
                });
    }
}
