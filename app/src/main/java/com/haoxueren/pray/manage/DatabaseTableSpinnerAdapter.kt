package com.haoxueren.pray.manage

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import com.haoxueren.pray.R

/**
 * 数据库表Spinner适配器
 */
class DatabaseTableSpinnerAdapter(
    private val context: Context,
    private val tables: MutableList<DatabaseTable> = mutableListOf()
) : BaseAdapter() {

    fun updateTables(newTables: List<DatabaseTable>) {
        tables.clear()
        tables.addAll(newTables)
        notifyDataSetChanged()
    }

    override fun getCount(): Int = tables.size

    override fun getItem(position: Int): DatabaseTable = tables[position]

    override fun getItemId(position: Int): Long = position.toLong()

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: LayoutInflater.from(context)
            .inflate(R.layout.item_table_spinner, parent, false)

        val table = tables[position]
        val tableNameTextView = view.findViewById<TextView>(R.id.tableNameTextView)

        // 在选中状态下只显示表名，节省空间
        tableNameTextView.text = table.tableName

        return view
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: LayoutInflater.from(context)
            .inflate(R.layout.item_table_spinner_dropdown, parent, false)
        
        val table = tables[position]
        val tableNameTextView = view.findViewById<TextView>(R.id.tableNameTextView)
        val recordCountTextView = view.findViewById<TextView>(R.id.recordCountTextView)
        
        tableNameTextView.text = table.tableName
        recordCountTextView.text = "${table.recordCount}条记录"
        
        return view
    }
}
