package com.haoxueren.pray.manage

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.haoxueren.pray.R
import com.haoxueren.sqlite.SQLiteHelper
import com.haoxueren.utils.Base16
import com.haoxueren.utils.ToastUtils
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers

/**
 * 数据库记录编辑对话框
 */
class DatabaseRecordEditDialog(
    private val context: Context,
    private val record: DatabaseRecord,
    private val tableName: String,
    private val onSaveSuccess: () -> Unit
) {

    private lateinit var dialog: BottomSheetDialog
    private lateinit var fieldsContainer: LinearLayout
    private lateinit var errorTextView: TextView
    private lateinit var saveButton: Button
    private lateinit var cancelButton: Button
    
    private val fieldEditTexts = mutableMapOf<String, EditText>()
    private val compositeDisposable = CompositeDisposable()
    
    // 不显示的字段
    private val hiddenFields = setOf("objectId", "createdAt", "updatedAt")
    
    fun show() {
        // 使用主题感知的BottomSheetDialog
        dialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_database_record_edit, null)
        dialog.setContentView(view)

        // 设置底部弹窗为全屏宽度
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let {
                val layoutParams = it.layoutParams
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
                it.layoutParams = layoutParams
            }
        }

        initViews(view)
        setupFields()
        setupButtons()

        dialog.show()
    }
    
    private fun initViews(view: View) {
        fieldsContainer = view.findViewById(R.id.fieldsContainer)
        errorTextView = view.findViewById(R.id.errorTextView)
        saveButton = view.findViewById(R.id.saveButton)
        cancelButton = view.findViewById(R.id.cancelButton)
        
        val titleTextView = view.findViewById<TextView>(R.id.titleTextView)
        titleTextView.text = "编辑 $tableName 记录"
    }
    
    private fun setupFields() {
        fieldsContainer.removeAllViews()
        fieldEditTexts.clear()
        
        for ((fieldName, fieldValue) in record.rawData) {
            if (fieldName == "tableName") continue // 跳过内部字段
            if (hiddenFields.contains(fieldName)) continue // 跳过隐藏字段

            val fieldView = LayoutInflater.from(context).inflate(R.layout.item_edit_field, fieldsContainer, false)
            val labelTextView = fieldView.findViewById<TextView>(R.id.fieldLabelTextView)
            val editText = fieldView.findViewById<EditText>(R.id.fieldValueEditText)

            // 设置字段标签
            labelTextView.text = getFieldDisplayName(fieldName)

            // 设置字段值
            val displayValue = formatFieldValue(fieldName, fieldValue)
            editText.setText(displayValue)

            // 根据字段类型设置输入类型
            setupInputType(fieldName, editText)

            fieldEditTexts[fieldName] = editText
            fieldsContainer.addView(fieldView)
        }
    }
    
    private fun getFieldDisplayName(fieldName: String): String {
        return when (fieldName) {
            "objectId" -> "对象ID"
            "id" -> "ID"
            "date" -> "日期"
            "count" -> "计数"
            "pray" -> "祷告内容"
            "groupId" -> "分组ID"
            "prayId" -> "祷告ID"
            "createdAt" -> "创建时间"
            "updatedAt" -> "更新时间"
            else -> fieldName
        }
    }
    
    private fun formatFieldValue(fieldName: String, fieldValue: Any?): String {
        if (fieldValue == null) return ""
        
        val valueStr = fieldValue.toString()
        
        // 对于祷告内容字段，尝试解码Base16
        if (fieldName == "pray" && Base16.isBase16(valueStr)) {
            return Base16.decode(valueStr)
        }
        
        return valueStr
    }
    
    private fun setupInputType(fieldName: String, editText: EditText) {
        when (fieldName) {
            "count", "groupId" -> {
                editText.inputType = android.text.InputType.TYPE_CLASS_NUMBER
            }
            "pray" -> {
                editText.inputType = android.text.InputType.TYPE_CLASS_TEXT or android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE
                editText.maxLines = 5
            }
            "date" -> {
                editText.hint = "格式: yyyy-MM-dd"
            }
            else -> {
                editText.inputType = android.text.InputType.TYPE_CLASS_TEXT
            }
        }
    }
    
    private fun setupButtons() {
        cancelButton.setOnClickListener {
            dialog.dismiss()
        }
        
        saveButton.setOnClickListener {
            saveRecord()
        }
    }
    
    private fun saveRecord() {
        hideError()
        
        // 验证输入
        val validationResult = validateInput()
        if (!validationResult.isValid) {
            showError(validationResult.errorMessage)
            return
        }
        
        // 收集更新的字段
        val updatedFields = mutableMapOf<String, Any?>()
        
        for ((fieldName, editText) in fieldEditTexts) {
            if (hiddenFields.contains(fieldName)) continue

            val newValue = editText.text.toString().trim()
            val originalValue = formatFieldValue(fieldName, record.rawData[fieldName])

            if (newValue != originalValue) {
                updatedFields[fieldName] = processFieldValue(fieldName, newValue)
            }
        }
        
        if (updatedFields.isEmpty()) {
            showError("没有检测到任何更改")
            return
        }
        
        // 执行更新操作
        performUpdate(updatedFields)
    }
    
    private fun validateInput(): ValidationResult {
        for ((fieldName, editText) in fieldEditTexts) {
            if (hiddenFields.contains(fieldName)) continue

            val value = editText.text.toString().trim()

            // 验证必填字段
            if (isRequiredField(fieldName) && value.isEmpty()) {
                return ValidationResult(false, "${getFieldDisplayName(fieldName)}不能为空")
            }

            // 验证数字字段
            if (isNumericField(fieldName) && value.isNotEmpty()) {
                try {
                    value.toInt()
                } catch (e: NumberFormatException) {
                    return ValidationResult(false, "${getFieldDisplayName(fieldName)}必须是有效的数字")
                }
            }

            // 验证日期格式
            if (fieldName == "date" && value.isNotEmpty()) {
                if (!isValidDateFormat(value)) {
                    return ValidationResult(false, "日期格式不正确，应为 yyyy-MM-dd 或 yyyy.MM.dd")
                }
            }
        }
        
        return ValidationResult(true, "")
    }
    
    private fun isRequiredField(fieldName: String): Boolean {
        return when (tableName) {
            "HaoPray" -> fieldName in setOf("id", "date", "pray")
            "HaoGroup" -> fieldName in setOf("groupId", "prayId")
            else -> false
        }
    }
    
    private fun isNumericField(fieldName: String): Boolean {
        return fieldName in setOf("count", "groupId")
    }
    
    private fun isValidDateFormat(date: String): Boolean {
        // 支持两种日期格式：yyyy-MM-dd 和 yyyy.MM.dd
        return date.matches(Regex("\\d{4}[-.]\\d{2}[-.]\\d{2}"))
    }
    
    private fun processFieldValue(fieldName: String, value: String): Any {
        return when {
            isNumericField(fieldName) -> {
                if (value.isEmpty()) 0 else value.toInt()
            }
            fieldName == "pray" -> {
                // 对祷告内容进行Base16编码
                Base16.encode(value)
            }
            else -> value
        }
    }
    
    private fun performUpdate(updatedFields: Map<String, Any?>) {
        saveButton.isEnabled = false
        saveButton.text = "保存中..."
        
        val objectId = record.rawData["objectId"]?.toString() ?: ""
        
        val disposable = Observable.fromCallable {
            when (tableName) {
                "HaoPray" -> SQLiteHelper.getInstance().updateHaoPrayFields(objectId, updatedFields)
                "HaoGroup" -> SQLiteHelper.getInstance().updateHaoGroupFields(objectId, updatedFields)
                else -> throw IllegalArgumentException("不支持的表: $tableName")
            }
        }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { affectedRows ->
                    if (affectedRows > 0) {
                        ToastUtils.showToast("更新成功")
                        dialog.dismiss()
                        onSaveSuccess()
                    } else {
                        showError("更新失败：未找到要更新的记录")
                        resetSaveButton()
                    }
                },
                { error ->
                    showError("更新失败：${error.message}")
                    resetSaveButton()
                }
            )
        
        compositeDisposable.add(disposable)
    }
    
    private fun resetSaveButton() {
        saveButton.isEnabled = true
        saveButton.text = "保存"
    }
    
    private fun showError(message: String) {
        errorTextView.text = message
        errorTextView.visibility = View.VISIBLE
    }
    
    private fun hideError() {
        errorTextView.visibility = View.GONE
    }
    
    fun dismiss() {
        compositeDisposable.clear()
        if (::dialog.isInitialized && dialog.isShowing) {
            dialog.dismiss()
        }
    }
    
    private data class ValidationResult(
        val isValid: Boolean,
        val errorMessage: String
    )
}
