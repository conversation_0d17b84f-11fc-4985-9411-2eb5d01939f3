package com.haoxueren.pray.manage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.PopupMenu
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.haoxueren.pray.R
import com.haoxueren.base.BaseActivity
import com.haoxueren.sqlite.SQLiteHelper
import com.haoxueren.utils.ToastUtils
import com.haoxueren.utils.ErrorUtils
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import io.reactivex.disposables.CompositeDisposable

/**
 * 数据库管理Activity
 */
class DatabaseManageActivity : BaseActivity() {

    private lateinit var toolbar: Toolbar
    private lateinit var tableButton: TextView
    private lateinit var fieldButton: TextView
    private lateinit var searchEditText: EditText
    private lateinit var searchButton: Button
    private lateinit var refreshLayout: SwipeRefreshLayout
    private lateinit var resultRecyclerView: RecyclerView

    private lateinit var recordAdapter: DatabaseRecordAdapter
    private var tables: MutableList<DatabaseTable> = mutableListOf()

    private var currentSelectedTable: String? = null
    private var currentSelectedField: String = "All"
    private var currentKeyword: String = ""
    private var currentPage: Int = 0
    private val pageSize: Int = 20
    private var isLoading: Boolean = false
    private var hasMoreData: Boolean = true
    private val compositeDisposable = CompositeDisposable()

    companion object {
        fun start(context: Context) {
            val intent = Intent(context, DatabaseManageActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.activity_database_manage
    }

    override fun bindView(layout: View) {
        toolbar = layout.findViewById(R.id.toolbar)
        tableButton = layout.findViewById(R.id.tableButton)
        fieldButton = layout.findViewById(R.id.fieldButton)
        searchEditText = layout.findViewById(R.id.searchEditText)
        searchButton = layout.findViewById(R.id.searchButton)
        refreshLayout = layout.findViewById(R.id.refreshLayout)
        resultRecyclerView = layout.findViewById(R.id.resultRecyclerView)
    }

    override fun initView() {
        // 设置Toolbar
        toolbar.setNavigationOnClickListener { finish() }

        // 初始化表选择按钮
        setupTableButton()

        // 初始化字段选择按钮
        setupFieldButton()

        // 初始化结果列表
        setupResultRecyclerView()
        
        // 设置搜索功能
        setupSearchFunction()
        
        // 设置下拉刷新
        setupRefreshLayout()
    }

    override fun initData() {
        loadTableList()
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable.clear()
    }

    private fun setupTableButton() {
        tableButton.setOnClickListener { view ->
            showTablePopupMenu(view)
        }
    }

    private fun showTablePopupMenu(anchor: View) {
        if (tables.isEmpty()) {
            ToastUtils.showToast("暂无可用数据表")
            return
        }

        val popupMenu = PopupMenu(this, anchor)
        
        // 动态添加菜单项
        tables.forEachIndexed { index, table ->
            popupMenu.menu.add(0, index, index, "${table.tableName} (${table.recordCount}条)")
        }
        
        popupMenu.setOnMenuItemClickListener { menuItem ->
            val position = menuItem.itemId
            val selectedTable = tables[position]
            onTableSelected(selectedTable)
            true
        }
        
        popupMenu.show()
    }

    private fun setupFieldButton() {
        fieldButton.setOnClickListener { view ->
            showFieldPopupMenu(view)
        }
    }

    private fun showFieldPopupMenu(anchor: View) {
        val disposable = Observable.fromCallable {
            SQLiteHelper.getInstance().getTableColumns(currentSelectedTable)
        }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({
                val fields = mutableListOf("All")
                val filteredFields = it.filter { field -> field != "objectId" && field != "updatedAt" && field != "createdAt" }
                fields.addAll(filteredFields)
                val popupMenu = PopupMenu(this, anchor)
                fields.forEachIndexed { index, field ->
                    popupMenu.menu.add(0, index, index, field)
                }
                popupMenu.setOnMenuItemClickListener { menuItem ->
                    val position = menuItem.itemId
                    val selectedField = fields[position]
                    onFieldSelected(selectedField)
                    true
                }
                popupMenu.show()
            }, {
                ToastUtils.showToast("Failed to load table fields")
            })
        compositeDisposable.add(disposable)
    }

    private fun onFieldSelected(field: String) {
        currentSelectedField = field
        fieldButton.text = field
    }

    private fun setupResultRecyclerView() {
        recordAdapter = DatabaseRecordAdapter { record ->
            onRecordClick(record)
        }
        val layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this)
        resultRecyclerView.layoutManager = layoutManager
        resultRecyclerView.adapter = recordAdapter

        // 添加滚动监听，实现分页加载
        resultRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val visibleItemCount = layoutManager.childCount
                val totalItemCount = layoutManager.itemCount
                val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                // 当滚动到底部时加载更多数据
                if (!isLoading && hasMoreData &&
                    (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                    loadMoreData()
                }
            }
        })
    }

    private fun setupSearchFunction() {
        searchButton.setOnClickListener {
            performSearch()
        }

        // 支持回车搜索
        searchEditText.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH) {
                performSearch()
                // 隐藏软键盘
                val imm = <EMAIL>(Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
                imm.hideSoftInputFromWindow(searchEditText.windowToken, 0)
                true
            } else {
                false
            }
        }

        // 添加文本变化监听，实现实时搜索提示
        searchEditText.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                val text = s?.toString()?.trim() ?: ""
                searchButton.isEnabled = currentSelectedTable != null

                if (text.length > 20) {
                    searchEditText.error = "搜索关键词不能超过20个字符"
                } else {
                    searchEditText.error = null
                }
            }
        })
    }

    private fun setupRefreshLayout() {
        refreshLayout.setOnRefreshListener {
            if (currentSelectedTable != null) {
                // 更新分页状态
                currentPage = 0
                hasMoreData = true
                currentKeyword = searchEditText.text.toString().trim()

                loadTableData(currentSelectedTable!!, currentKeyword, currentSelectedField, true)
            } else {
                refreshLayout.isRefreshing = false
            }
        }
    }

    private fun loadTableList() {
        val disposable = Observable.fromCallable {
            try {
                SQLiteHelper.getInstance().tableInfoList
            } catch (e: Exception) {
                throw Exception("获取表信息失败", e)
            }
        }
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .subscribe({ tableInfoList ->
            try {
                tables.clear()
                tables.addAll(tableInfoList.map { info ->
                    DatabaseTable(
                        tableName = info["tableName"] as String,
                        recordCount = info["recordCount"] as Int
                    )
                })

                // 如果有表数据，自动选择第一个表
                if (tables.isNotEmpty()) {
                    // 更新按钮文本显示第一个表
                    tableButton.text = tables[0].tableName
                    onTableSelected(tables[0])
                } else {
                    tableButton.text = "选择表"
                    ToastUtils.showToast("数据库中没有找到表")
                }
            } catch (e: Exception) {
                ErrorUtils.onError(e)
                ToastUtils.showToast("解析表信息失败")
            }
        }, { error ->
            ErrorUtils.onError(error)
            ToastUtils.showToast("加载表信息失败: ${error.message}")
        })

        compositeDisposable.add(disposable)
    }

    private fun onTableSelected(table: DatabaseTable) {
        currentSelectedTable = table.tableName
        tableButton.text = table.tableName
        toolbar.subtitle = "${table.tableName} (${table.recordCount})"

        // 重置分页状态
        currentPage = 0
        hasMoreData = true
        currentKeyword = ""

        // 清空搜索框和结果
        searchEditText.setText("")
        recordAdapter.updateRecords(emptyList())

        // 加载表数据
        loadTableData(table.tableName, "", "All", true)
    }

    private fun performSearch() {
        val keyword = searchEditText.text.toString().trim()

        if (currentSelectedTable == null) {
            ToastUtils.showToast("请先选择要查询的表")
            return
        }

        if (keyword.length > 20) {
            ToastUtils.showToast("搜索关键词不能超过20个字符")
            return
        }

        if (isLoading) {
            ToastUtils.showToast("正在查询中，请稍候...")
            return
        }

        // 重置分页状态
        currentPage = 0
        hasMoreData = true
        currentKeyword = keyword

        // 清空现有结果
        recordAdapter.updateRecords(emptyList())

        loadTableData(currentSelectedTable!!, keyword, currentSelectedField, true)
    }

    private fun loadTableData(tableName: String, keyword: String, field: String, isRefresh: Boolean = false) {
        if (isLoading) return

        isLoading = true
        if (isRefresh) {
            refreshLayout.isRefreshing = true
        }

        val offset = currentPage * pageSize

        val disposable = Observable.fromCallable {
            try {
                val count = SQLiteHelper.getInstance().searchInTableCount(tableName, keyword, field)
                val records = if (keyword.isEmpty()) {
                    // 显示最新的记录
                    SQLiteHelper.getInstance().getLatestRecords(tableName, pageSize, offset)
                } else {
                    // 根据关键词搜索
                    SQLiteHelper.getInstance().searchInTable(tableName, keyword, field, pageSize, offset)
                }
                Pair(count, records)
            } catch (e: Exception) {
                throw Exception("查询数据失败", e)
            }
        }
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .subscribe({ (count, rawRecords) ->
            val records = rawRecords.map { rawData ->
                convertToDisplayRecord(tableName, rawData)
            }

            if (isRefresh) {
                recordAdapter.updateRecords(records)
            } else {
                recordAdapter.addRecords(records)
            }

            // 更新Toolbar的副标题
            val table = tables.find { it.tableName == tableName }
            if (keyword.isEmpty()) {
                toolbar.subtitle = "${table?.tableName} (${table?.recordCount})"
            } else {
                toolbar.subtitle = "${table?.tableName} ($count)"
            }

            // 更新分页状态
            hasMoreData = records.size == pageSize
            currentPage++

            isLoading = false
            refreshLayout.isRefreshing = false

            if (records.isEmpty() && isRefresh) {
                if (keyword.isEmpty()) {
                    ToastUtils.showToast("该表暂无数据")
                } else {
                    ToastUtils.showToast("未找到匹配的记录")
                }
            } else if (isRefresh) {
                val message = if (keyword.isEmpty()) {
                    "加载了 ${records.size} 条最新记录"
                } else {
                    "找到 ${records.size} 条匹配记录"
                }
                if (hasMoreData) {
                    ToastUtils.showToast("$message，向下滚动加载更多")
                } else {
                    ToastUtils.showToast(message)
                }
            }
        }, { error ->
            ErrorUtils.onError(error)
            isLoading = false
            refreshLayout.isRefreshing = false
            ToastUtils.showToast("查询失败: ${error.message}")
        })

        compositeDisposable.add(disposable)
    }

    private fun loadMoreData() {
        if (currentSelectedTable != null && hasMoreData && !isLoading) {
            loadTableData(currentSelectedTable!!, currentKeyword, currentSelectedField, false)
        }
    }

    /**
     * 将原始数据转换为显示记录
     */
    private fun convertToDisplayRecord(tableName: String, rawData: Map<String, Any?>): DatabaseRecord {
        val mutableRawData = rawData.toMutableMap()
        mutableRawData["tableName"] = tableName

        return when (tableName) {
            DatabaseTable.TABLE_HAO_PRAY -> {
                val pray = rawData["pray"]?.toString() ?: ""
                val decodedPray = try {
                    // 尝试解码Base16编码的内容
                    com.haoxueren.utils.Base16.decode(pray)
                } catch (e: Exception) {
                    pray // 如果解码失败，使用原始内容
                }
                val date = rawData["date"]?.toString() ?: ""
                val count = rawData["count"]?.toString() ?: "0"
                val record = String.format("%s.%s %s", date, count, decodedPray)

                DatabaseRecord(
                    primaryField = record,
                    secondaryField = "",
                    timestamp = rawData["createAt"]?.toString() ?: "",
                    rawData = mutableRawData
                )
            }
            DatabaseTable.TABLE_HAO_GROUP -> {
                val pray = rawData["pray"]?.toString() ?: ""
                val prayId = rawData["prayId"]?.toString() ?: ""
                val decodedPray = try {
                    // 尝试解码Base16编码的内容
                    com.haoxueren.utils.Base16.decode(pray)
                } catch (e: Exception) {
                    pray // 如果解码失败，使用原始内容
                }

                DatabaseRecord(
                    primaryField = if (decodedPray.isNotEmpty()) decodedPray else prayId,
                    secondaryField = "",
                    timestamp = rawData["createAt"]?.toString() ?: "",
                    rawData = mutableRawData
                )
            }
            else -> {
                val previewData = rawData.entries.take(3).joinToString(" | ") { "${it.key}: ${it.value}" }
                DatabaseRecord(
                    primaryField = previewData,
                    secondaryField = "",
                    timestamp = rawData["createAt"]?.toString() ?: "",
                    rawData = mutableRawData
                )
            }
        }
    }

    /**
     * 处理记录点击事件，弹出编辑对话框
     */
    private fun onRecordClick(record: DatabaseRecord) {
        val tableName = currentSelectedTable ?: return

        val editDialog = DatabaseRecordEditDialog(
            context = this,
            record = record,
            tableName = tableName,
            onSaveSuccess = {
                // 保存成功后刷新当前页面
                refreshCurrentData()
            }
        )

        editDialog.show()
    }

    /**
     * 刷新当前数据
     */
    private fun refreshCurrentData() {
        currentPage = 0
        hasMoreData = true
        loadTableData(currentSelectedTable ?: return, currentKeyword, currentSelectedField, true)
    }
}
