package com.haoxueren.pray.manage

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.haoxueren.pray.R
import com.haoxueren.view.HorizontalScrollTextView

/**
 * 数据库查询结果适配器
 */
class DatabaseRecordAdapter(
    private val onRecordClick: (DatabaseRecord) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val records = mutableListOf<DatabaseRecord>()
    private var showEmpty = true

    companion object {
        private const val TYPE_EMPTY = 0
        private const val TYPE_RECORD = 1
        private const val TYPE_GROUP_RECORD = 2
    }

    fun updateRecords(newRecords: List<DatabaseRecord>) {
        records.clear()
        records.addAll(newRecords)
        showEmpty = records.isEmpty()
        notifyDataSetChanged()
    }

    fun addRecords(newRecords: List<DatabaseRecord>) {
        val startPosition = records.size
        records.addAll(newRecords)
        showEmpty = false
        notifyItemRangeInserted(startPosition, newRecords.size)
    }

    override fun getItemViewType(position: Int): Int {
        if (showEmpty && records.isEmpty()) {
            return TYPE_EMPTY
        }
        val record = records[position]
        return if (record.rawData["tableName"] == DatabaseTable.TABLE_HAO_GROUP) {
            TYPE_GROUP_RECORD
        } else {
            TYPE_RECORD
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_EMPTY -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.layout_database_empty, parent, false)
                EmptyViewHolder(view)
            }
            TYPE_GROUP_RECORD -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_database_group_record, parent, false)
                GroupRecordViewHolder(view)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_database_record, parent, false)
                RecordViewHolder(view)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is RecordViewHolder -> {
                val record = records[position]
                holder.bind(record, onRecordClick)
            }
            is GroupRecordViewHolder -> {
                val record = records[position]
                holder.bind(record, onRecordClick)
            }
            is EmptyViewHolder -> {
                // 空状态不需要绑定数据
            }
        }
    }

    override fun getItemCount(): Int = if (showEmpty && records.isEmpty()) 1 else records.size

    class RecordViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val primaryFieldTextView: TextView = itemView.findViewById(R.id.primaryFieldTextView)

        fun bind(record: DatabaseRecord, onRecordClick: (DatabaseRecord) -> Unit) {
            primaryFieldTextView.text = record.primaryField

            // 改为长按事件
            itemView.setOnLongClickListener {
                onRecordClick(record)
                true // 返回true表示消费了长按事件
            }
        }
    }

    class GroupRecordViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val groupIdView: TextView = itemView.findViewById(R.id.groupIdView)
        private val prayTextView: HorizontalScrollTextView = itemView.findViewById(R.id.prayTextView)

        fun bind(record: DatabaseRecord, onRecordClick: (DatabaseRecord) -> Unit) {
            groupIdView.text = String.format("%s.", record.rawData["groupId"]?.toString() ?: "")
            prayTextView.setText(record.primaryField)

            // 为整个itemView设置长按事件
            itemView.setOnLongClickListener {
                onRecordClick(record)
                true // 返回true表示消费了长按事件
            }

            // 为groupIdView也设置长按事件
            groupIdView.setOnLongClickListener {
                onRecordClick(record)
                true
            }

            // 为prayTextView设置长按事件
            prayTextView.setOnLongClickListener {
                onRecordClick(record)
                true
            }

            // 优化prayTextView的触摸行为，保持横向滑动功能
            prayTextView.isFocusable = false
            prayTextView.isFocusableInTouchMode = false
            prayTextView.isClickable = false // 禁用点击，只保留长按
        }
    }

    class EmptyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
