package com.haoxueren.pray.manage

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.haoxueren.pray.R
import com.haoxueren.pray.bean.DateBean
import com.haoxueren.sqlite.SQLiteHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import androidx.core.content.ContextCompat
import androidx.compose.foundation.BorderStroke

class StatisticsActivity : ComponentActivity() {

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, StatisticsActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            // TODO: 如果项目中定义了实际主题，请替换此处
            MaterialTheme {
                StatisticsScreen()
            }
        }
    }
}

@Composable
fun StatisticsScreen() {
    val context = LocalContext.current // 用于返回操作和资源获取的上下文
    var dateBeans by remember { mutableStateOf<List<DateBean>>(emptyList()) }

    LaunchedEffect(Unit) {
        val data = withContext(Dispatchers.IO) {
            SQLiteHelper.getInstance().groupByDate() // 已修正：此处不需要上下文
        }
        dateBeans = data
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("统计", fontSize = 18.sp) },
                navigationIcon = {
                    IconButton(onClick = {
                        // 安全的返回操作处理
                        (context as? ComponentActivity)?.onBackPressedDispatcher?.onBackPressed()
                    }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                backgroundColor = colorResource(id = R.color.theme),
                contentColor = Color.White
            )
        }
    ) { paddingValues ->
        LazyVerticalGrid(
            columns = GridCells.Fixed(2), // 设置为两列
            modifier = Modifier
                .padding(paddingValues) // 应用 Scaffold 提供的 padding
                .fillMaxSize() // 填充可用空间
                .padding(horizontal = dimensionResource(id = R.dimen.margin_default)),
            contentPadding = PaddingValues(all = 8.dp) // 为网格项添加一些间距
        ) {
            items(dateBeans) { bean ->
                StatisticsItem(dateBean = bean)
            }
        }
    }
}

@Composable
fun StatisticsItem(dateBean: DateBean) {
    // 使用 Card 组件创建带有边框的项目
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        elevation = 1.dp,
        border = BorderStroke(0.5.dp, Color.LightGray)
    ) {
        // 用于容纳内容的列布局
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(dimensionResource(id = R.dimen.padding_default)),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = dateBean.date ?: "N/A",
                    fontSize = 14.sp
                )
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    text = dateBean.count ?: "N/A",
                    fontSize = 14.sp
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    MaterialTheme {
        val sampleData = remember {
            listOf(
                DateBean().apply { date = "2024-05-01"; count = "12" },
                DateBean().apply { date = "2024-05-02"; count = "8" }
            )
        }
        
        // 在实际应用中，您会向 StatisticsScreen 传递数据或使用 ViewModel。
        // 为了预览目的，我们可以模拟状态或直接传递（如果 Composable 允许）。
        // 此预览设置显示了包含示例数据的列表。

        var previewDateBeans by remember { mutableStateOf(sampleData) }

        val context = LocalContext.current
        val appBarBackgroundColor = remember(context) {
            try {
                // androidx.core.content.ContextCompat
                // import androidx.core.content.ContextCompat
                // 如果尚未导入，则需要添加此导入。
                // 目前，假设它可以被解析或将由 IDE 添加。
                // 如果 ContextCompat 不可用，则可以使用直接的 Color。
                // 为了简化此自动化步骤，我将对资源进行直接检查。
                // 对于预览而言，更可靠的方法可能是直接定义颜色。
                // 然而，为了遵循加载 R.color.theme 的初衷：
                // 这里仍然使用 colorResource，让我们改变策略。

                // 正确方法：如果上下文允许，则使用 ContextCompat 获取颜色。
                // 对于 @Preview，colorResource 通常没问题，但 lint 是问题所在。
                // lint 专门针对围绕可组合函数调用的 try-catch。
                // 因此，围绕 colorResource() 的原始 try-catch 结构是问题所在。

                // 让我们使用 ContextCompat.getColor 并转换为 Compose Color
                // 这需要为 androidx.core.content.ContextCompat 添加导入
                androidx.compose.ui.graphics.Color(ContextCompat.getColor(context, R.color.theme))
            } catch (e: Exception) {
                Color.Gray //备用颜色
            }
        }

        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text("统计 Preview") },
                    navigationIcon = { Icon(Icons.AutoMirrored.Filled.ArrowBack, "Back") },
                    backgroundColor = appBarBackgroundColor,
                    contentColor = Color.White
                )
            }
        ) { paddingValues ->
            LazyColumn(
                modifier = Modifier
                    .padding(paddingValues)
                    .padding(horizontal = dimensionResource(id = R.dimen.margin_default) // 如果可解析，则使用实际尺寸资源
                    // 如果在预览上下文中无法解析尺寸资源，则使用回退值
                    // .padding(horizontal = 16.dp) 
                )
            ) {
                items(previewDateBeans) { bean ->
                    StatisticsItem(dateBean = bean)
                }
            }
        }
    }
}