package com.haoxueren.pray.manage

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.haoxueren.pray.R

/**
 * 数据库表列表适配器
 */
class DatabaseTableAdapter(
    private val onTableClick: (DatabaseTable) -> Unit
) : RecyclerView.Adapter<DatabaseTableAdapter.TableViewHolder>() {

    private val tables = mutableListOf<DatabaseTable>()
    private var selectedPosition = -1

    fun updateTables(newTables: List<DatabaseTable>) {
        tables.clear()
        tables.addAll(newTables)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TableViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_database_table, parent, false)
        return TableViewHolder(view)
    }

    override fun onBindViewHolder(holder: TableViewHolder, position: Int) {
        val table = tables[position]
        holder.bind(table, position == selectedPosition)
        
        holder.itemView.setOnClickListener {
            val oldPosition = selectedPosition
            selectedPosition = position
            
            // 刷新选中状态
            if (oldPosition != -1) {
                notifyItemChanged(oldPosition)
            }
            notifyItemChanged(selectedPosition)
            
            onTableClick(table)
        }
    }

    override fun getItemCount(): Int = tables.size

    class TableViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tableNameTextView: TextView = itemView.findViewById(R.id.tableNameTextView)
        private val recordCountTextView: TextView = itemView.findViewById(R.id.recordCountTextView)

        fun bind(table: DatabaseTable, isSelected: Boolean) {
            tableNameTextView.text = table.tableName
            recordCountTextView.text = "记录数: ${table.recordCount}"
            
            // 设置选中状态的背景
            itemView.isSelected = isSelected
            if (isSelected) {
                itemView.setBackgroundResource(R.color.bg_tertiary)
            } else {
                itemView.setBackgroundResource(android.R.color.transparent)
            }
        }
    }
}
