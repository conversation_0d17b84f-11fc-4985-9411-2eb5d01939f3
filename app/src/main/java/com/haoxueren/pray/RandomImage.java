package com.haoxueren.pray;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.haoxueren.utils.ContextManager;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Objects;
import java.util.function.Consumer;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class RandomImage {

    public static RequestBuilder<Drawable> loadImage(Context context) {
        File file = new File(context.getCacheDir(), "random.png");
        return Glide.with(context)
                .load(file)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE);
    }

    public static void cacheImage(String imageUrl) {
        Observable.create(RandomImage.downloadImage(imageUrl)::accept)
                .subscribeOn(Schedulers.io())
                .doOnNext(RandomImage::saveImage)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result->{},e->{});
    }

    private static Consumer<ObservableEmitter<Bitmap>> downloadImage(String imageUrl) {
        return emitter -> {
            try {
                OkHttpClient client = new OkHttpClient();
                Request request = new Request.Builder()
                        .url(imageUrl).get().build();
                Call call = client.newCall(request);
                Response response = call.execute();
                InputStream inputStream = response.body().byteStream();
                Bitmap bitmap = BitmapFactory.decodeStream(inputStream);
                Objects.requireNonNull(bitmap);
                emitter.onNext(bitmap);
            } catch (IOException e) {
                emitter.onError(e);
            }
        };
    }

    private static void saveImage(Bitmap bitmap) throws FileNotFoundException {
        Context context = ContextManager.getContext();
        File cacheDir = context.getCacheDir();
        File file = new File(cacheDir, "random.png");
        OutputStream outputStream = new FileOutputStream(file);
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
    }
}
