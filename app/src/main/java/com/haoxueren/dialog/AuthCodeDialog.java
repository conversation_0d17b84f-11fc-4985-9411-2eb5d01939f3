package com.haoxueren.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;

import androidx.annotation.NonNull;

import com.haoxueren.pray.R;
import com.haoxueren.utils.PreferenceUtils;
import com.haoxueren.utils.SafetyUtils;

import java.util.function.Consumer;

/**
 * 动态密码校验对话框
 */
public class AuthCodeDialog extends Dialog {

    private Consumer<Boolean> dismiss;

    public AuthCodeDialog(@NonNull Activity activity) {
        super(activity);
        this.setActivityAlpha(activity, 0f);
        this.setContentView(R.layout.dialog_auth_code);
        this.setCancelable(false);
        EditText authCodeView = this.findViewById(R.id.authCodeView);
        View confirmView = this.findViewById(R.id.confirmView);
        confirmView.setOnClickListener(v -> {
            String authCode = authCodeView.getText().toString();
            CharSequence dateCode = SafetyUtils.getAuthCode();
            if (TextUtils.equals(authCode, dateCode)) {
                PreferenceUtils.putString("auth_code", authCode);
                this.setActivityAlpha(activity, 1f);
                dismiss.accept(true);
                this.dismiss();
            } else {
                dismiss.accept(false);
                this.dismiss();
            }
        });
    }

    public void show(Consumer<Boolean> dismiss) {
        this.dismiss = dismiss;
        this.show();
    }

    private void setActivityAlpha(Activity activity, float alpha) {
        Window window = activity.getWindow();
        WindowManager.LayoutParams attributes = window.getAttributes();
        attributes.alpha = alpha;
        window.setAttributes(attributes);
    }

}
