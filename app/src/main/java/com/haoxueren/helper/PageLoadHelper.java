package com.haoxueren.helper;


import com.haoxueren.java8.Action;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * 下拉刷新，上拉加载的逻辑框架
 * create by haomi<PERSON><PERSON><PERSON> on 2020/4/22
 */
public class PageLoadHelper<T> {

    private List<T> data; // 列表数据集合

    private int page, size; // 要加载的页数

    private boolean hasMoreData = true; // 是否还有更多数据

    public PageLoadHelper(List<T> list, int size) {
        this.data = list;
        this.size = size;
        this.page = 1;
    }

    /**
     * 下拉刷新时调用
     */
    public void onRefresh(BiConsumer<Integer, Integer> request) {
        request.accept(1, size);
    }

    public boolean hasMoreData() {
        return hasMoreData;
    }

    /**
     * 上拉加载时调用
     * 有更多数据时请求服务器，无更多数据时完成加载
     */
    public void onLoadMore(BiConsumer<Integer, Integer> request) {
        if (hasMoreData) {
            int tempPage = page + 1;
            request.accept(tempPage, size);
        }
    }

    /**
     * 数据加载成功后调用，需要指明是否为上拉加载
     */
    public void onSuccess(List<T> newData, boolean isLoadMore, Action action) {
        if (isLoadMore) {
            this.onLoadMoreSuccess(newData, action);
        } else {
            this.onRefreshSuccess(newData, action);
        }
    }

    /**
     * 下拉刷新成功后调用
     */
    private void onRefreshSuccess(List<T> newData, Action action) {
        hasMoreData = newData.size() == size; // 是否有更多数据
        page = 1;
        data.clear();
        data.addAll(newData);
        action.run();
    }

    /**
     * 上拉加载成功时调用
     */
    private void onLoadMoreSuccess(List<T> newData, Action action) {
        hasMoreData = newData.size() == size; // 是否有更多数据
        page = newData.isEmpty() ? page : page + 1; // 页码加 1
        data.addAll(newData);
        action.run();
    }

}
