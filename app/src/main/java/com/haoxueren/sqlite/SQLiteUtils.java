package com.haoxueren.sqlite;

import android.text.TextUtils;

import com.haoxueren.utils.Base16;
import com.haoxueren.utils.DateUtils;
import com.haoxueren.utils.DigestUtils;

public class SQLiteUtils {

    public static boolean isParamsEmpty(String... params) {
        if (params == null || params.length == 0) {
            return true;
        }
        for (String param : params) {
            if (!TextUtils.isEmpty(param)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 生成 IN 语法参数
     */
    public static String getInParams(String... tags) {
        StringBuilder builder = new StringBuilder();
        for (String tag : tags) {
            if (builder.length() > 0) {
                builder.append(",");
            }
            builder.append("'").append(tag).append("'").append(",")
                    .append("'").append(Base16.encode(tag)).append("'");
        }
        return builder.toString();
    }

    /**
     * 生成长度10位的objectId
     */
    public static String newObjectId(String source) {
        long millis = System.currentTimeMillis();
        try {
            return DigestUtils.md5(source + millis).substring(0, 10);
        } catch (Exception e) {
            e.printStackTrace();
            return Base16.encode(source + millis);
        }
    }

    /**
     * 生成记录更新时间
     */
    public static String getUpdatedAt() {
        return DateUtils.today("yyyy-MM-dd HH:mm:ss");
    }
}
