package com.haoxueren.sqlite;

import android.content.Context;
import androidx.appcompat.app.AlertDialog;
import com.haoxueren.utils.ToastUtils;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.io.IOException;

/**
 * 数据库同步错误处理器
 * 提供详细的错误分析和用户友好的错误信息
 */
public class SyncErrorHandler {

    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        NETWORK_CONNECTION_FAILED,    // 网络连接失败
        NETWORK_TIMEOUT,             // 网络超时
        DEVICE_NOT_FOUND,            // 设备未找到
        DATABASE_VALIDATION_FAILED,   // 数据库验证失败
        FILE_OPERATION_FAILED,       // 文件操作失败
        BACKUP_FAILED,               // 备份失败
        RESTORE_FAILED,              // 恢复失败
        UNKNOWN_ERROR                // 未知错误
    }

    /**
     * 错误信息类
     */
    public static class ErrorInfo {
        public ErrorType type;
        public String title;
        public String message;
        public String suggestion;
        public boolean canRetry;
        public boolean canRestore;

        public ErrorInfo(ErrorType type, String title, String message, String suggestion, 
                        boolean canRetry, boolean canRestore) {
            this.type = type;
            this.title = title;
            this.message = message;
            this.suggestion = suggestion;
            this.canRetry = canRetry;
            this.canRestore = canRestore;
        }
    }

    /**
     * 分析异常并返回错误信息
     */
    public static ErrorInfo analyzeError(Throwable error) {
        if (error instanceof ConnectException) {
            return new ErrorInfo(
                ErrorType.NETWORK_CONNECTION_FAILED,
                "连接失败",
                "无法连接到目标设备",
                "请检查：\n1. 设备是否在同一网络\n2. 目标设备是否开启了同步服务\n3. IP地址和端口是否正确",
                true,
                false
            );
        } else if (error instanceof SocketTimeoutException) {
            return new ErrorInfo(
                ErrorType.NETWORK_TIMEOUT,
                "连接超时",
                "网络连接超时",
                "请检查：\n1. 网络连接是否稳定\n2. 目标设备是否响应正常\n3. 尝试重新连接",
                true,
                false
            );
        } else if (error instanceof UnknownHostException) {
            return new ErrorInfo(
                ErrorType.DEVICE_NOT_FOUND,
                "设备未找到",
                "无法找到指定的设备",
                "请检查：\n1. IP地址是否正确\n2. 设备是否在线\n3. 网络连接是否正常",
                true,
                false
            );
        } else if (error instanceof IOException) {
            String message = error.getMessage();
            if (message != null) {
                if (message.contains("数据库文件无效") || message.contains("备份文件已损坏")) {
                    return new ErrorInfo(
                        ErrorType.DATABASE_VALIDATION_FAILED,
                        "数据库验证失败",
                        "下载的数据库文件无效或已损坏",
                        "建议：\n1. 重新尝试同步\n2. 检查源设备的数据库是否正常\n3. 联系技术支持",
                        true,
                        true
                    );
                } else if (message.contains("无法替换数据库文件")) {
                    return new ErrorInfo(
                        ErrorType.FILE_OPERATION_FAILED,
                        "文件操作失败",
                        "无法替换数据库文件",
                        "可能原因：\n1. 文件被其他程序占用\n2. 存储空间不足\n3. 权限不足",
                        true,
                        true
                    );
                } else if (message.contains("备份")) {
                    return new ErrorInfo(
                        ErrorType.BACKUP_FAILED,
                        "备份失败",
                        "无法创建数据库备份",
                        "建议：\n1. 检查存储空间是否充足\n2. 重启应用后重试\n3. 手动备份数据库",
                        true,
                        false
                    );
                }
            }
            
            return new ErrorInfo(
                ErrorType.FILE_OPERATION_FAILED,
                "文件操作失败",
                "文件操作过程中发生错误",
                "建议：\n1. 检查存储空间\n2. 重启应用\n3. 重新尝试操作",
                true,
                true
            );
        } else {
            return new ErrorInfo(
                ErrorType.UNKNOWN_ERROR,
                "未知错误",
                "发生了未知错误：" + (error.getMessage() != null ? error.getMessage() : error.getClass().getSimpleName()),
                "建议：\n1. 重启应用\n2. 重新尝试操作\n3. 如果问题持续，请联系技术支持",
                true,
                true
            );
        }
    }

    /**
     * 显示错误对话框
     */
    public static void showErrorDialog(Context context, Throwable error, 
                                     Runnable onRetry, Runnable onRestore) {
        ErrorInfo errorInfo = analyzeError(error);
        showErrorDialog(context, errorInfo, onRetry, onRestore);
    }

    /**
     * 显示错误对话框
     */
    public static void showErrorDialog(Context context, ErrorInfo errorInfo, 
                                     Runnable onRetry, Runnable onRestore) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context)
            .setTitle(errorInfo.title)
            .setMessage(errorInfo.message + "\n\n" + errorInfo.suggestion)
            .setPositiveButton("确定", null);

        if (errorInfo.canRetry && onRetry != null) {
            builder.setNeutralButton("重试", (dialog, which) -> onRetry.run());
        }

        if (errorInfo.canRestore && onRestore != null) {
            builder.setNegativeButton("恢复备份", (dialog, which) -> onRestore.run());
        }

        builder.show();
    }

    /**
     * 显示简单的错误提示
     */
    public static void showErrorToast(Throwable error) {
        ErrorInfo errorInfo = analyzeError(error);
        ToastUtils.showToast(errorInfo.title + ": " + errorInfo.message);
    }

    /**
     * 获取用户友好的错误消息
     */
    public static String getUserFriendlyMessage(Throwable error) {
        ErrorInfo errorInfo = analyzeError(error);
        return errorInfo.message;
    }

    /**
     * 检查错误是否可以重试
     */
    public static boolean canRetry(Throwable error) {
        ErrorInfo errorInfo = analyzeError(error);
        return errorInfo.canRetry;
    }

    /**
     * 检查错误是否可以恢复备份
     */
    public static boolean canRestore(Throwable error) {
        ErrorInfo errorInfo = analyzeError(error);
        return errorInfo.canRestore;
    }

    /**
     * 记录详细的错误信息（用于调试）
     */
    public static void logError(String operation, Throwable error) {
        ErrorInfo errorInfo = analyzeError(error);
        
        System.err.println("=== 数据库同步错误 ===");
        System.err.println("操作: " + operation);
        System.err.println("错误类型: " + errorInfo.type);
        System.err.println("错误标题: " + errorInfo.title);
        System.err.println("错误消息: " + errorInfo.message);
        System.err.println("建议: " + errorInfo.suggestion);
        System.err.println("可重试: " + errorInfo.canRetry);
        System.err.println("可恢复: " + errorInfo.canRestore);
        System.err.println("异常详情:");
        error.printStackTrace();
        System.err.println("==================");
    }

    /**
     * 网络连接状态检查
     */
    public static class NetworkChecker {
        
        /**
         * 检查设备连接状态
         */
        public static void checkDeviceConnection(DatabaseSyncManager.DeviceInfo device, 
                                               ConnectionCallback callback) {
            DeviceDiscoveryManager.getInstance().checkDeviceConnection(device)
                .subscribe(
                    isOnline -> {
                        if (isOnline) {
                            callback.onSuccess();
                        } else {
                            callback.onFailure(new ConnectException("设备离线或无法访问"));
                        }
                    },
                    error -> callback.onFailure(error)
                );
        }
        
        public interface ConnectionCallback {
            void onSuccess();
            void onFailure(Throwable error);
        }
    }

    /**
     * 同步状态监控器
     */
    public static class SyncMonitor {
        private long startTime;
        private String currentOperation;
        
        public void startOperation(String operation) {
            this.currentOperation = operation;
            this.startTime = System.currentTimeMillis();
            System.out.println("开始操作: " + operation);
        }
        
        public void endOperation(boolean success) {
            long duration = System.currentTimeMillis() - startTime;
            System.out.println("操作完成: " + currentOperation + 
                             ", 耗时: " + duration + "ms" + 
                             ", 结果: " + (success ? "成功" : "失败"));
        }
        
        public void logProgress(String message, int progress) {
            System.out.println("进度更新: " + message + " (" + progress + "%)");
        }
    }
}
