package com.haoxueren.sqlite;

import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.haoxueren.pray.MyApplication;
import com.haoxueren.restful.GsonUtils;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

/**
 * 设备发现和管理器
 * 负责局域网内设备的自动发现、连接状态监控等功能
 */
public class DeviceDiscoveryManager {

    private static volatile DeviceDiscoveryManager instance;
    private Context context;
    private List<DatabaseSyncManager.DeviceInfo> discoveredDevices;
    private boolean isScanning = false;
    private Disposable scanDisposable;
    
    // 默认同步端口
    private static final int DEFAULT_SYNC_PORT = 2024;
    private static final int DISCOVERY_PORT = 2025;
    private static final int SCAN_TIMEOUT = 2000; // 2秒超时
    private static final String DISCOVERY_MESSAGE = "HAOPRAY_DISCOVERY";
    private static final String DISCOVERY_RESPONSE = "HAOPRAY_DEVICE";
    
    public static DeviceDiscoveryManager getInstance() {
        if (instance == null) {
            synchronized (DeviceDiscoveryManager.class) {
                if (instance == null) {
                    instance = new DeviceDiscoveryManager();
                }
            }
        }
        return instance;
    }
    
    private DeviceDiscoveryManager() {
        this.context = MyApplication.getContext();
        this.discoveredDevices = new CopyOnWriteArrayList<>();
    }
    
    /**
     * 设备发现回调接口
     */
    public interface DiscoveryCallback {
        void onDeviceFound(DatabaseSyncManager.DeviceInfo device);
        void onScanProgress(String message);
        void onScanComplete(List<DatabaseSyncManager.DeviceInfo> devices);
        void onError(String error);
    }
    
    /**
     * 开始扫描局域网设备
     */
    public void startDeviceDiscovery(DiscoveryCallback callback) {
        if (isScanning) {
            callback.onError("正在扫描中，请稍候...");
            return;
        }

        isScanning = true;
        discoveredDevices.clear();

        scanDisposable = Observable.<List<DatabaseSyncManager.DeviceInfo>>create(emitter -> {
            try {
                String localIp = getLocalIpAddress();
                if (TextUtils.isEmpty(localIp)) {
                    emitter.onError(new Exception("无法获取本机IP地址"));
                    return;
                }

                callback.onScanProgress("本机IP: " + localIp + "，开始扫描局域网设备...");

                // 首先尝试UDP广播发现
                callback.onScanProgress("正在进行UDP广播发现...");
                startUdpDiscovery(callback);

                // 等待1秒让UDP发现完成
                Thread.sleep(1000);

                // 然后进行TCP端口扫描
                callback.onScanProgress("正在进行TCP端口扫描...");

                // 获取网络段
                String networkPrefix = localIp.substring(0, localIp.lastIndexOf('.') + 1);

                List<Observable<DatabaseSyncManager.DeviceInfo>> scanTasks = new ArrayList<>();

                // 扫描网络段内的所有IP
                for (int i = 1; i <= 254; i++) {
                    String targetIp = networkPrefix + i;
                    if (!targetIp.equals(localIp)) { // 跳过本机IP
                        scanTasks.add(scanSingleDevice(targetIp, DEFAULT_SYNC_PORT));
                    }
                }

                // 并行扫描所有IP
                Observable.merge(scanTasks)
                    .filter(device -> device != null)
                    .subscribe(
                        device -> {
                            // 检查是否已经通过UDP发现了这个设备
                            boolean alreadyFound = false;
                            for (DatabaseSyncManager.DeviceInfo existing : discoveredDevices) {
                                if (existing.ip.equals(device.ip)) {
                                    alreadyFound = true;
                                    break;
                                }
                            }
                            if (!alreadyFound) {
                                discoveredDevices.add(device);
                                callback.onDeviceFound(device);
                            }
                        },
                        error -> {
                            // 忽略单个设备扫描错误
                        },
                        () -> {
                            emitter.onNext(new ArrayList<>(discoveredDevices));
                            emitter.onComplete();
                        }
                    );

            } catch (Exception e) {
                emitter.onError(e);
            }
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .subscribe(
            devices -> {
                isScanning = false;
                callback.onScanComplete(devices);
            },
            error -> {
                isScanning = false;
                callback.onError("扫描失败: " + error.getMessage());
            }
        );
    }
    
    /**
     * UDP广播发现设备
     */
    private void startUdpDiscovery(DiscoveryCallback callback) {
        new Thread(() -> {
            DatagramSocket socket = null;
            try {
                // 创建UDP socket
                socket = new DatagramSocket();
                socket.setBroadcast(true);
                socket.setSoTimeout(1000); // 1秒超时，用于接收响应

                String localIp = getLocalIpAddress();
                android.util.Log.d("DeviceDiscovery", "开始UDP广播发现，本机IP: " + localIp);

                // 发送多次广播以提高发现成功率
                for (int attempt = 0; attempt < 3; attempt++) {
                    try {
                        // 发送到全网广播地址
                        sendUdpBroadcast(socket, "***************", DISCOVERY_PORT);

                        // 发送到本地网段广播地址
                        if (localIp != null && !localIp.isEmpty()) {
                            String networkPrefix = localIp.substring(0, localIp.lastIndexOf('.'));
                            String localBroadcast = networkPrefix + ".255";
                            sendUdpBroadcast(socket, localBroadcast, DISCOVERY_PORT);

                            // 如果默认端口失败，尝试备用端口
                            for (int port = DISCOVERY_PORT + 1; port <= DISCOVERY_PORT + 10; port++) {
                                sendUdpBroadcast(socket, localBroadcast, port);
                            }
                        }

                        android.util.Log.d("DeviceDiscovery", "UDP广播发送完成，尝试 " + (attempt + 1) + "/3");

                        // 短暂等待再发送下一次
                        if (attempt < 2) {
                            Thread.sleep(500);
                        }
                    } catch (Exception e) {
                        android.util.Log.w("DeviceDiscovery", "UDP广播发送失败，尝试 " + (attempt + 1), e);
                    }
                }

                // 监听响应
                byte[] responseBuffer = new byte[1024];
                long startTime = System.currentTimeMillis();
                int responseCount = 0;

                while (System.currentTimeMillis() - startTime < 5000) { // 监听5秒
                    try {
                        DatagramPacket responsePacket = new DatagramPacket(responseBuffer, responseBuffer.length);
                        socket.receive(responsePacket);

                        String response = new String(responsePacket.getData(), 0, responsePacket.getLength());
                        String deviceIp = responsePacket.getAddress().getHostAddress();

                        android.util.Log.d("DeviceDiscovery", "收到UDP响应: " + response + " 来自: " + deviceIp);

                        if (response.startsWith(DISCOVERY_RESPONSE)) {
                            responseCount++;

                            // 检查是否是本机设备，如果是则跳过
                            if (isLocalDevice(deviceIp)) {
                                android.util.Log.d("DeviceDiscovery", "跳过本机设备: " + deviceIp);
                                continue;
                            }

                            // 创建设备信息
                            DatabaseSyncManager.DeviceInfo device = new DatabaseSyncManager.DeviceInfo(
                                "HaoPray设备 (" + deviceIp + ")", deviceIp, DEFAULT_SYNC_PORT);

                            // 检查是否已经发现过这个设备
                            boolean alreadyFound = false;
                            for (DatabaseSyncManager.DeviceInfo existing : discoveredDevices) {
                                if (existing.ip.equals(device.ip)) {
                                    alreadyFound = true;
                                    break;
                                }
                            }

                            if (!alreadyFound) {
                                discoveredDevices.add(device);
                                callback.onDeviceFound(device);
                                android.util.Log.i("DeviceDiscovery", "发现新设备: " + deviceIp);
                            }
                        }
                    } catch (SocketTimeoutException e) {
                        // 超时是正常的，继续监听
                    } catch (Exception e) {
                        android.util.Log.w("DeviceDiscovery", "UDP响应处理错误", e);
                    }
                }

                android.util.Log.d("DeviceDiscovery", "UDP发现完成，收到 " + responseCount + " 个响应");

            } catch (Exception e) {
                android.util.Log.e("DeviceDiscovery", "UDP广播发现失败", e);
            } finally {
                if (socket != null) {
                    socket.close();
                }
            }
        }).start();
    }

    /**
     * 发送UDP广播消息
     */
    private void sendUdpBroadcast(DatagramSocket socket, String broadcastAddress, int port) {
        try {
            String message = DISCOVERY_MESSAGE;
            byte[] buffer = message.getBytes();
            InetAddress address = InetAddress.getByName(broadcastAddress);
            DatagramPacket packet = new DatagramPacket(buffer, buffer.length, address, port);
            socket.send(packet);
            android.util.Log.d("DeviceDiscovery", "发送UDP广播到: " + broadcastAddress + ":" + port);
        } catch (Exception e) {
            android.util.Log.w("DeviceDiscovery", "发送UDP广播失败: " + broadcastAddress + ":" + port, e);
        }
    }

    /**
     * 扫描单个设备
     */
    private Observable<DatabaseSyncManager.DeviceInfo> scanSingleDevice(String ip, int port) {
        return Observable.<DatabaseSyncManager.DeviceInfo>create(emitter -> {
            Socket socket = null;
            try {
                socket = new Socket();
                socket.connect(new java.net.InetSocketAddress(ip, port), SCAN_TIMEOUT);

                // 发送设备信息请求
                JsonObject request = new JsonObject();
                request.addProperty("action", "device_info");

                java.io.OutputStream out = socket.getOutputStream();
                out.write(request.toString().getBytes());
                out.flush();

                // 读取响应
                java.io.InputStream in = socket.getInputStream();
                byte[] buffer = new byte[1024];
                int bytesRead = in.read(buffer);
                
                if (bytesRead > 0) {
                    String response = new String(buffer, 0, bytesRead);
                    try {
                        Gson gson = GsonUtils.getGson();
                        JsonObject responseJson = gson.fromJson(response, JsonObject.class);
                        
                        String deviceName = responseJson.has("device_name") ? 
                            responseJson.get("device_name").getAsString() : "未知设备";
                        
                        DatabaseSyncManager.DeviceInfo device = 
                            new DatabaseSyncManager.DeviceInfo(deviceName, ip, port);
                        
                        emitter.onNext(device);
                    } catch (Exception e) {
                        // 如果响应格式不正确，仍然创建设备信息
                        DatabaseSyncManager.DeviceInfo device = 
                            new DatabaseSyncManager.DeviceInfo("HaoPray设备", ip, port);
                        emitter.onNext(device);
                    }
                }
                // 无论是否有响应，都在finally块中完成

            } catch (SocketTimeoutException e) {
                // 连接超时，设备不在线
            } catch (Exception e) {
                // 连接异常
            } finally {
                if (socket != null) {
                    try {
                        socket.close();
                    } catch (IOException e) {
                        // 忽略关闭异常
                    }
                }
                // 无论成功还是失败，都完成Observable
                emitter.onComplete();
            }
        })
        .subscribeOn(Schedulers.io())
        .timeout(SCAN_TIMEOUT + 1000, TimeUnit.MILLISECONDS)
        .onErrorReturn(error -> null);
    }
    
    /**
     * 停止设备扫描
     */
    public void stopDeviceDiscovery() {
        if (scanDisposable != null && !scanDisposable.isDisposed()) {
            scanDisposable.dispose();
        }
        isScanning = false;
    }
    
    /**
     * 检查设备连接状态
     */
    public Observable<Boolean> checkDeviceConnection(DatabaseSyncManager.DeviceInfo device) {
        return Observable.<Boolean>create(emitter -> {
            Socket socket = null;
            try {
                socket = new Socket();
                socket.connect(new java.net.InetSocketAddress(device.ip, device.port), SCAN_TIMEOUT);
                
                // 发送ping请求
                JsonObject request = new JsonObject();
                request.addProperty("action", "ping");
                
                java.io.OutputStream out = socket.getOutputStream();
                out.write(request.toString().getBytes());
                out.flush();
                
                emitter.onNext(true);
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onNext(false);
                emitter.onComplete();
            } finally {
                if (socket != null) {
                    try {
                        socket.close();
                    } catch (IOException e) {
                        // 忽略关闭异常
                    }
                }
            }
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .timeout(SCAN_TIMEOUT, TimeUnit.MILLISECONDS)
        .onErrorReturn(error -> false);
    }
    
    // UDP响应服务器状态管理
    private volatile boolean udpServerRunning = false;
    private DatagramSocket udpServerSocket = null;
    private Thread udpServerThread = null;

    /**
     * 启动UDP响应服务器
     */
    public void startUdpResponseServer() {
        if (udpServerRunning) {
            android.util.Log.d("DeviceDiscovery", "UDP响应服务器已在运行");
            return;
        }

        udpServerThread = new Thread(() -> {
            DatagramSocket socket = null;
            try {
                // 尝试绑定端口，如果失败则尝试其他端口
                socket = createUdpSocket();
                if (socket == null) {
                    android.util.Log.e("DeviceDiscovery", "无法创建UDP socket");
                    return;
                }

                udpServerSocket = socket;
                udpServerRunning = true;
                android.util.Log.d("DeviceDiscovery", "UDP响应服务器启动成功，端口: " + socket.getLocalPort());

                byte[] buffer = new byte[1024];

                while (udpServerRunning) {
                    try {
                        DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                        socket.receive(packet);

                        String message = new String(packet.getData(), 0, packet.getLength());
                        android.util.Log.d("DeviceDiscovery", "收到UDP消息: " + message + " 来自: " + packet.getAddress().getHostAddress());

                        if (DISCOVERY_MESSAGE.equals(message)) {
                            // 响应发现请求
                            String localIp = getLocalIpAddress();
                            String response = DISCOVERY_RESPONSE + "_" + localIp;
                            byte[] responseData = response.getBytes();

                            InetAddress clientAddress = packet.getAddress();
                            int clientPort = packet.getPort();

                            DatagramPacket responsePacket = new DatagramPacket(
                                responseData, responseData.length, clientAddress, clientPort);
                            socket.send(responsePacket);

                            android.util.Log.d("DeviceDiscovery", "发送UDP响应: " + response + " 到: " + clientAddress.getHostAddress() + ":" + clientPort);
                        }
                    } catch (java.net.SocketTimeoutException e) {
                        // 超时是正常的，继续监听
                    } catch (Exception e) {
                        if (udpServerRunning) {
                            android.util.Log.e("DeviceDiscovery", "UDP服务器处理请求错误", e);
                        }
                    }
                }

            } catch (Exception e) {
                android.util.Log.e("DeviceDiscovery", "UDP响应服务器启动失败", e);
            } finally {
                udpServerRunning = false;
                if (socket != null) {
                    socket.close();
                }
                android.util.Log.d("DeviceDiscovery", "UDP响应服务器已停止");
            }
        });

        udpServerThread.setName("UDP-Response-Server");
        udpServerThread.start();
    }

    /**
     * 创建UDP socket，支持端口重试
     */
    private DatagramSocket createUdpSocket() {
        // 首先尝试默认端口
        try {
            DatagramSocket socket = new DatagramSocket(DISCOVERY_PORT);
            socket.setSoTimeout(1000); // 设置1秒超时
            return socket;
        } catch (Exception e) {
            android.util.Log.w("DeviceDiscovery", "端口 " + DISCOVERY_PORT + " 被占用，尝试其他端口");
        }

        // 如果默认端口被占用，尝试其他端口
        for (int port = DISCOVERY_PORT + 1; port <= DISCOVERY_PORT + 10; port++) {
            try {
                DatagramSocket socket = new DatagramSocket(port);
                socket.setSoTimeout(1000);
                android.util.Log.i("DeviceDiscovery", "使用备用端口: " + port);
                return socket;
            } catch (Exception e) {
                // 继续尝试下一个端口
            }
        }

        return null;
    }

    /**
     * 停止UDP响应服务器
     */
    public void stopUdpResponseServer() {
        udpServerRunning = false;
        if (udpServerSocket != null) {
            udpServerSocket.close();
        }
        if (udpServerThread != null) {
            udpServerThread.interrupt();
        }
        android.util.Log.d("DeviceDiscovery", "UDP响应服务器停止请求已发送");
    }

    /**
     * 检查UDP响应服务器状态
     */
    public boolean isUdpServerRunning() {
        return udpServerRunning;
    }

    /**
     * 获取本机IP地址
     */
    private String getLocalIpAddress() {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext()
                .getSystemService(Context.WIFI_SERVICE);
            
            if (wifiManager != null) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                int ipAddress = wifiInfo.getIpAddress();
                
                if (ipAddress != 0) {
                    return String.format("%d.%d.%d.%d",
                        (ipAddress & 0xff),
                        (ipAddress >> 8 & 0xff),
                        (ipAddress >> 16 & 0xff),
                        (ipAddress >> 24 & 0xff));
                }
            }
            
            // 备用方法：通过网络接口获取
            java.util.Enumeration<java.net.NetworkInterface> interfaces = 
                java.net.NetworkInterface.getNetworkInterfaces();
            
            while (interfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = interfaces.nextElement();
                
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                
                java.util.Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    
                    if (!address.isLoopbackAddress() && address instanceof java.net.Inet4Address) {
                        return address.getHostAddress();
                    }
                }
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * 获取已发现的设备列表
     */
    public List<DatabaseSyncManager.DeviceInfo> getDiscoveredDevices() {
        return new ArrayList<>(discoveredDevices);
    }
    
    /**
     * 添加手动设备
     */
    public void addManualDevice(String name, String ip, int port) {
        // 检查是否是本机设备
        if (isLocalDevice(ip)) {
            android.util.Log.w("DeviceDiscovery", "不能添加本机设备: " + ip);
            return;
        }

        DatabaseSyncManager.DeviceInfo device = new DatabaseSyncManager.DeviceInfo(name, ip, port);

        // 检查是否已存在
        for (DatabaseSyncManager.DeviceInfo existing : discoveredDevices) {
            if (existing.ip.equals(ip) && existing.port == port) {
                return; // 已存在，不重复添加
            }
        }

        discoveredDevices.add(device);
    }
    
    /**
     * 移除设备
     */
    public void removeDevice(DatabaseSyncManager.DeviceInfo device) {
        discoveredDevices.remove(device);
    }
    
    /**
     * 清空设备列表
     */
    public void clearDevices() {
        discoveredDevices.clear();
    }

    /**
     * 移除本机设备（如果存在）
     */
    public void removeLocalDevice() {
        String localIp = getLocalIpAddress();
        if (localIp == null || localIp.isEmpty()) {
            return;
        }

        Iterator<DatabaseSyncManager.DeviceInfo> iterator = discoveredDevices.iterator();
        while (iterator.hasNext()) {
            DatabaseSyncManager.DeviceInfo device = iterator.next();
            if (isLocalDevice(device.ip)) {
                iterator.remove();
                android.util.Log.d("DeviceDiscovery", "移除本机设备: " + device.ip);
            }
        }
    }

    /**
     * 检查IP是否是本机设备
     */
    private boolean isLocalDevice(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        // 检查主要的本机IP
        String localIp = getLocalIpAddress();
        if (ip.equals(localIp)) {
            return true;
        }

        // 检查常见的本机地址
        if (ip.equals("127.0.0.1") || ip.equals("localhost")) {
            return true;
        }

        // 检查所有网络接口的IP地址
        try {
            java.util.Enumeration<java.net.NetworkInterface> interfaces = java.net.NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = interfaces.nextElement();
                java.util.Enumeration<java.net.InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    java.net.InetAddress address = addresses.nextElement();
                    if (ip.equals(address.getHostAddress())) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            android.util.Log.w("DeviceDiscovery", "检查本机IP时出错", e);
        }

        return false;
    }
    
    /**
     * 检查是否正在扫描
     */
    public boolean isScanning() {
        return isScanning;
    }
    
    /**
     * 获取本机设备信息
     */
    public DatabaseSyncManager.DeviceInfo getLocalDeviceInfo() {
        String localIp = getLocalIpAddress();
        if (!TextUtils.isEmpty(localIp)) {
            String deviceName = android.os.Build.MODEL + " (本机)";
            DatabaseSyncManager.DeviceInfo localDevice = new DatabaseSyncManager.DeviceInfo(
                deviceName,
                localIp,
                DEFAULT_SYNC_PORT
            );
            // 设置为在线状态
            localDevice.isOnline = true;
            localDevice.lastSeen = System.currentTimeMillis();
            return localDevice;
        }
        return null;
    }

    /**
     * 获取详细的本机设备信息
     */
    public String getDetailedLocalDeviceInfo() {
        StringBuilder info = new StringBuilder();

        try {
            // 设备基本信息
            info.append("设备型号: ").append(android.os.Build.MODEL).append("\n");
            info.append("制造商: ").append(android.os.Build.MANUFACTURER).append("\n");
            info.append("Android版本: ").append(android.os.Build.VERSION.RELEASE).append("\n");

            // 网络信息
            String localIp = getLocalIpAddress();
            if (!TextUtils.isEmpty(localIp)) {
                info.append("本机IP: ").append(localIp).append("\n");

                // 网络段信息
                String networkPrefix = localIp.substring(0, localIp.lastIndexOf('.'));
                info.append("网络段: ").append(networkPrefix).append(".x\n");
            } else {
                info.append("本机IP: 未获取到\n");
            }

            // WiFi信息
            if (context != null) {
                WifiManager wifiManager = (WifiManager) context.getApplicationContext()
                    .getSystemService(Context.WIFI_SERVICE);
                if (wifiManager != null) {
                    WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                    String ssid = wifiInfo.getSSID();
                    if (ssid != null && !ssid.equals("<unknown ssid>")) {
                        info.append("WiFi网络: ").append(ssid).append("\n");
                    }
                }
            }

            // 服务状态
            info.append("同步端口: ").append(DEFAULT_SYNC_PORT).append("\n");
            info.append("发现端口: ").append(DISCOVERY_PORT).append("\n");
            info.append("UDP服务器: ").append(isUdpServerRunning() ? "运行中" : "未运行").append("\n");

        } catch (Exception e) {
            info.append("获取设备信息时出错: ").append(e.getMessage());
        }

        return info.toString();
    }
}
