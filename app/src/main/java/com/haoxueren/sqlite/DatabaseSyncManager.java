package com.haoxueren.sqlite;

import android.content.Context;
import android.text.TextUtils;

import com.haoxueren.pray.MyApplication;
import com.haoxueren.utils.DateUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 数据库同步管理器
 * 负责数据库的导出、导入、备份、恢复等核心功能
 */
public class DatabaseSyncManager {

    private static volatile DatabaseSyncManager instance;
    private Context context;
    
    // 数据库文件路径
    private static final String DB_NAME = "HaoPray.db";
    private static final String TEMP_DB_NAME = "HaoPray_temp.db";
    private static final String BACKUP_DB_PREFIX = "HaoPray_backup_";
    
    public static DatabaseSyncManager getInstance() {
        if (instance == null) {
            synchronized (DatabaseSyncManager.class) {
                if (instance == null) {
                    instance = new DatabaseSyncManager();
                }
            }
        }
        return instance;
    }
    
    private DatabaseSyncManager() {
        this.context = MyApplication.getContext();
    }
    
    /**
     * 同步状态回调接口
     */
    public interface SyncCallback {
        void onProgress(String message, int progress);
        void onSuccess(String message);
        void onError(String error, Throwable throwable);
    }
    
    /**
     * 设备信息类
     */
    public static class DeviceInfo {
        public String name;
        public String ip;
        public int port;
        public boolean isOnline;
        public long lastSeen;
        
        public DeviceInfo(String name, String ip, int port) {
            this.name = name;
            this.ip = ip;
            this.port = port;
            this.isOnline = true;
            this.lastSeen = System.currentTimeMillis();
        }
        
        @Override
        public String toString() {
            return name + " (" + ip + ":" + port + ")";
        }
    }
    
    /**
     * 获取当前数据库文件
     */
    public File getCurrentDatabase() {
        return context.getDatabasePath(DB_NAME);
    }
    
    /**
     * 创建数据库备份
     */
    public Observable<File> createBackup() {
        return Observable.<File>create(emitter -> {
            try {
                // 关闭数据库连接
                SQLiteHelper.getInstance().close();
                
                File sourceDb = getCurrentDatabase();
                if (!sourceDb.exists()) {
                    emitter.onError(new IOException("数据库文件不存在"));
                    return;
                }
                
                // 创建备份文件
                String timestamp = DateUtils.today("yyyyMMdd_HHmmss");
                File backupFile = new File(sourceDb.getParent(), BACKUP_DB_PREFIX + timestamp + ".db");
                
                // 复制文件
                copyFile(sourceDb, backupFile);
                
                emitter.onNext(backupFile);
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * 恢复数据库备份
     */
    public Observable<Boolean> restoreBackup(File backupFile) {
        return Observable.<Boolean>create(emitter -> {
            try {
                if (!backupFile.exists()) {
                    emitter.onError(new IOException("备份文件不存在"));
                    return;
                }

                // 验证备份文件
                validateDatabase(backupFile).subscribe(isValid -> {
                    if (!isValid) {
                        emitter.onError(new IOException("备份文件已损坏"));
                        return;
                    }

                    try {
                        // 关闭数据库连接
                        SQLiteHelper.getInstance().close();

                        File currentDb = getCurrentDatabase();

                        // 复制备份文件到当前数据库位置
                        copyFile(backupFile, currentDb);

                        emitter.onNext(true);
                        emitter.onComplete();

                    } catch (Exception e) {
                        emitter.onError(e);
                    }
                }, error -> emitter.onError(error));

            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取最新的备份文件
     */
    public Observable<File> getLatestBackup() {
        return Observable.<File>create(emitter -> {
            try {
                File dbDir = getCurrentDatabase().getParentFile();
                if (dbDir == null) {
                    emitter.onNext(null);
                    emitter.onComplete();
                    return;
                }

                File[] backupFiles = dbDir.listFiles((dir, name) ->
                    name.startsWith(BACKUP_DB_PREFIX) && name.endsWith(".db"));

                if (backupFiles == null || backupFiles.length == 0) {
                    emitter.onNext(null);
                    emitter.onComplete();
                    return;
                }

                // 按修改时间排序，获取最新的备份
                java.util.Arrays.sort(backupFiles, (f1, f2) ->
                    Long.compare(f2.lastModified(), f1.lastModified()));

                emitter.onNext(backupFiles[0]);
                emitter.onComplete();

            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 自动恢复最新备份（用于同步失败时）
     */
    public Observable<Boolean> autoRestoreLatestBackup() {
        return getLatestBackup().flatMap(latestBackup -> {
            if (latestBackup != null) {
                return restoreBackup(latestBackup);
            } else {
                return Observable.error(new IOException("没有可用的备份文件"));
            }
        });
    }
    
    /**
     * 验证数据库文件完整性
     */
    public Observable<Boolean> validateDatabase(File dbFile) {
        return Observable.<Boolean>create(emitter -> {
            try {
                if (!dbFile.exists()) {
                    emitter.onNext(false);
                    emitter.onComplete();
                    return;
                }
                
                // 检查文件大小
                if (dbFile.length() == 0) {
                    emitter.onNext(false);
                    emitter.onComplete();
                    return;
                }
                
                // 尝试打开数据库验证格式
                android.database.sqlite.SQLiteDatabase testDb = null;
                try {
                    testDb = android.database.sqlite.SQLiteDatabase.openDatabase(
                        dbFile.getPath(), null, android.database.sqlite.SQLiteDatabase.OPEN_READONLY);
                    
                    // 检查表是否存在
                    android.database.Cursor cursor = testDb.rawQuery(
                        "SELECT name FROM sqlite_master WHERE type='table' AND name IN ('HaoPray', 'HaoGroup')", null);
                    
                    int tableCount = 0;
                    while (cursor.moveToNext()) {
                        tableCount++;
                    }
                    cursor.close();
                    
                    boolean isValid = tableCount >= 2; // 至少包含两个主要表
                    emitter.onNext(isValid);
                    emitter.onComplete();
                    
                } finally {
                    if (testDb != null) {
                        testDb.close();
                    }
                }
                
            } catch (Exception e) {
                emitter.onNext(false);
                emitter.onComplete();
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * 计算文件MD5校验和
     */
    public Observable<String> calculateMD5(File file) {
        return Observable.<String>create(emitter -> {
            try {
                MessageDigest md = MessageDigest.getInstance("MD5");
                FileInputStream fis = new FileInputStream(file);
                byte[] buffer = new byte[8192];
                int bytesRead;
                
                while ((bytesRead = fis.read(buffer)) != -1) {
                    md.update(buffer, 0, bytesRead);
                }
                fis.close();
                
                byte[] digest = md.digest();
                StringBuilder sb = new StringBuilder();
                for (byte b : digest) {
                    sb.append(String.format("%02x", b));
                }
                
                emitter.onNext(sb.toString());
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * 复制文件
     */
    private void copyFile(File source, File dest) throws IOException {
        try (FileInputStream inStream = new FileInputStream(source);
             FileOutputStream outStream = new FileOutputStream(dest);
             FileChannel inChannel = inStream.getChannel();
             FileChannel outChannel = outStream.getChannel()) {
            
            inChannel.transferTo(0, inChannel.size(), outChannel);
        }
    }
    
    /**
     * 清理临时文件
     */
    public void cleanupTempFiles() {
        try {
            File tempDb = context.getDatabasePath(TEMP_DB_NAME);
            if (tempDb.exists()) {
                tempDb.delete();
            }
            
            // 清理过期的备份文件（保留最近5个）
            cleanupOldBackups();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 清理旧的备份文件
     */
    private void cleanupOldBackups() {
        try {
            File dbDir = getCurrentDatabase().getParentFile();
            if (dbDir == null) return;
            
            File[] backupFiles = dbDir.listFiles((dir, name) -> 
                name.startsWith(BACKUP_DB_PREFIX) && name.endsWith(".db"));
            
            if (backupFiles != null && backupFiles.length > 5) {
                // 按修改时间排序，删除最旧的文件
                java.util.Arrays.sort(backupFiles, (f1, f2) -> 
                    Long.compare(f1.lastModified(), f2.lastModified()));
                
                for (int i = 0; i < backupFiles.length - 5; i++) {
                    backupFiles[i].delete();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 获取数据库信息
     */
    public Observable<DatabaseInfo> getDatabaseInfo() {
        return Observable.<DatabaseInfo>create(emitter -> {
            try {
                File dbFile = getCurrentDatabase();
                DatabaseInfo info = new DatabaseInfo();
                info.filePath = dbFile.getAbsolutePath();
                info.fileSize = dbFile.length();
                info.lastModified = dbFile.lastModified();
                info.exists = dbFile.exists();
                
                if (info.exists) {
                    // 获取记录数量
                    android.database.sqlite.SQLiteDatabase db = 
                        android.database.sqlite.SQLiteDatabase.openDatabase(
                            dbFile.getPath(), null, android.database.sqlite.SQLiteDatabase.OPEN_READONLY);
                    
                    android.database.Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM HaoPray", null);
                    if (cursor.moveToFirst()) {
                        info.prayRecordCount = cursor.getInt(0);
                    }
                    cursor.close();
                    
                    cursor = db.rawQuery("SELECT COUNT(*) FROM HaoGroup", null);
                    if (cursor.moveToFirst()) {
                        info.groupRecordCount = cursor.getInt(0);
                    }
                    cursor.close();
                    
                    db.close();
                }
                
                emitter.onNext(info);
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * 数据库信息类
     */
    public static class DatabaseInfo {
        public String filePath;
        public long fileSize;
        public long lastModified;
        public boolean exists;
        public int prayRecordCount;
        public int groupRecordCount;
        
        public String getFormattedSize() {
            if (fileSize < 1024) {
                return fileSize + " B";
            } else if (fileSize < 1024 * 1024) {
                return String.format("%.1f KB", fileSize / 1024.0);
            } else {
                return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
            }
        }
        
        public String getFormattedDate() {
            return DateUtils.format(lastModified, "yyyy-MM-dd HH:mm:ss");
        }
    }
}
