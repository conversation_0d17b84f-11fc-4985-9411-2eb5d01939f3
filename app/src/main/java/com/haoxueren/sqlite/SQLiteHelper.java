package com.haoxueren.sqlite;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;


import com.haoxueren.pray.bean.DateBean;
import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.group.HaoPrayGroup;
import com.haoxueren.utils.ContextManager;
import com.haoxueren.utils.DateUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * SQLite数据库操作帮助类
 */
public class SQLiteHelper {

    private static volatile SQLiteHelper instance;

    public static SQLiteHelper getInstance() {
        if (instance == null) {
            instance = new SQLiteHelper(ContextManager.getContext());
        }
        return instance;
    }

    private Context context;
    private SQLiteDatabase database;

    private SQLiteHelper(Context context) {
        if (database == null) {
            this.context = context.getApplicationContext();
            File databasePath = context.getDatabasePath("HaoPray.db");
            if (!databasePath.exists()) {
                database = SQLiteDatabase.openOrCreateDatabase(databasePath.getPath(), null);
                this.createTable(database);
            } else {
                database = SQLiteDatabase.openDatabase(databasePath.getPath(), null, 0);
            }
        }
    }


    public SQLiteDatabase getDatabase() {
        if (database == null) {
            File databasePath = context.getDatabasePath("HaoPray.db");
            database = SQLiteDatabase.openDatabase(databasePath.getPath(), null, 0);
        }
        return database;
    }

    public void close() {
        if (database != null) {
            database.close();
            database = null;
        }
    }

    private void createTable(SQLiteDatabase db) {
        String group = "CREATE TABLE HaoGroup (\n" +
                "    objectId  VARCHAR UNIQUE\n" +
                "                      PRIMARY KEY,\n" +
                "    groupId   INTEGER,\n" +
                "    prayId    VARCHAR,\n" +
                "    pray      TEXT,\n" +
                "    createdAt DATE,\n" +
                "    updatedAt DATE\n" +
                ");";
        String pray = "CREATE TABLE HaoPray (\n" +
                "    objectId  VARCHAR UNIQUE\n" +
                "                      PRIMARY KEY,\n" +
                "    id        VARCHAR,\n" +
                "    date      VARCHAR,\n" +
                "    count     INTEGER,\n" +
                "    pray      TEXT,\n" +
                "    updatedAt DATE,\n" +
                "    createdAt DATE\n" +
                ");";
        db.execSQL(group);
        db.execSQL(pray);
    }

    public List<HaoPrayGroup> queryHaoGroup(int fromId) {
        SQLiteDatabase database = this.getDatabase();
        List<HaoPrayGroup> results = new ArrayList<>();
        Cursor cursor = database.rawQuery("select * from HaoGroup where groupId>=?", new String[]{fromId + ""});
        CursorProxy proxy = new CursorProxy(cursor);
        while (proxy.moveToNext()) {
            int groupId = proxy.getInt("groupId");
            String prayId = proxy.getString("prayId");
            String prayText = proxy.getString("pray");
            HaoPrayGroup bean = new HaoPrayGroup(groupId, prayId, prayText);
            bean.setObjectId(proxy.getString("objectId"));
            bean.setCreatedAt(proxy.getString("createdAt"));
            bean.setUpdatedAt(proxy.getString("updatedAt"));
            results.add(bean);
        }
        return results;
    }

    public Observable<String> insertHaoPray(HaoPray haoPray) {
        SQLiteDatabase database = this.getDatabase();
        database.execSQL("INSERT INTO HaoPray (objectId,id, date, count, pray,createdAt,updatedAt) VALUES ( ?, ?,?,?,?,?,?);"
                , new String[]{haoPray.getObjectId(), haoPray.getId(), haoPray.getDate(), haoPray.getCount() + "", haoPray.getEncodePray(),
                        haoPray.getCreatedAt(), DateUtils.today("yyyy-MM-dd HH:mm:ss")});
        return Observable.just(haoPray.getPray());
    }

    public void deleteHaoPray() {

    }

    /**
     * 更新HaoPray记录
     * @param haoPray 要更新的HaoPray对象
     * @return 更新影响的行数
     */
    public int updateHaoPray(HaoPray haoPray) {
        if (TextUtils.isEmpty(haoPray.getObjectId())) {
            throw new IllegalArgumentException("objectId is empty!");
        }

        SQLiteDatabase database = getDatabase();
        ContentValues values = new ContentValues();
        values.put("id", haoPray.getId());
        values.put("date", haoPray.getDate());
        values.put("count", haoPray.getCount());
        values.put("pray", haoPray.getEncodePray());
        values.put("updatedAt", DateUtils.today("yyyy-MM-dd HH:mm:ss"));

        return database.update("HaoPray", values, "objectId=?", new String[]{haoPray.getObjectId()});
    }

    /**
     * 根据objectId更新HaoPray记录的指定字段
     * @param objectId 记录的objectId
     * @param fieldValues 要更新的字段和值的映射
     * @return 更新影响的行数
     */
    public int updateHaoPrayFields(String objectId, Map<String, Object> fieldValues) {
        if (TextUtils.isEmpty(objectId)) {
            throw new IllegalArgumentException("objectId is empty!");
        }
        if (fieldValues == null || fieldValues.isEmpty()) {
            throw new IllegalArgumentException("fieldValues is empty!");
        }

        SQLiteDatabase database = getDatabase();
        ContentValues values = new ContentValues();

        for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                values.put(key, (String) value);
            } else if (value instanceof Integer) {
                values.put(key, (Integer) value);
            } else if (value instanceof Long) {
                values.put(key, (Long) value);
            } else if (value instanceof Boolean) {
                values.put(key, (Boolean) value);
            } else if (value != null) {
                values.put(key, value.toString());
            }
        }

        // 自动更新updatedAt字段
        values.put("updatedAt", DateUtils.today("yyyy-MM-dd HH:mm:ss"));

        return database.update("HaoPray", values, "objectId=?", new String[]{objectId});
    }

    public List<HaoPray> queryHaoPray(String id, int skip, int limit) {
        SQLiteDatabase database = this.getDatabase();
        ArrayList<HaoPray> results = new ArrayList<>();
        Cursor cursor = database.rawQuery("select * from HaoPray where id=? order by date desc limit ? offset ?", new String[]{id, limit + "", skip + ""});
        CursorProxy proxy = new CursorProxy(cursor);
        while (proxy.moveToNext()) {
            HaoPray bean = new HaoPray();
            bean.setObjectId(proxy.getString("objectId"));
            bean.setId(proxy.getString("id"));
            bean.setDate(proxy.getString("date"));
            bean.setCount(proxy.getString("count"));
            bean.setPray(proxy.getString("pray"));
            results.add(bean);
        }
        cursor.close();
        return results;
    }


    public Observable<Map<String, Integer>> queryIdCount(String id) {
        SQLiteDatabase database = this.getDatabase();
        Cursor cursor = database.rawQuery("select id, count(id) from HaoPray group by id", null);
        CursorProxy proxy = new CursorProxy(cursor);
        Map<String, Integer> map = new HashMap<>();
        while (proxy.moveToNext()) {
            String prayId = proxy.getString("id");
            int count = proxy.getInt("count(id)");
            map.put(prayId, count);
        }
        return Observable.just(map);
    }

    public long insertHaoGroup(Integer groupId, String prayId, String pray) {
        SQLiteUtils.newObjectId(groupId + "" + System.currentTimeMillis());
        String objectId = SQLiteUtils.newObjectId(groupId + "" + System.currentTimeMillis());
        ContentValues values = new ContentValues();
        values.put("objectId", objectId);
        values.put("groupId", groupId);
        values.put("prayId", prayId);
        values.put("pray", pray);
        values.put("createdAt", SQLiteUtils.getUpdatedAt());
        values.put("updatedAt", SQLiteUtils.getUpdatedAt());
        return getDatabase().insert("HaoGroup", null, values);
    }

    /**
     * 更新目标分组对象信息
     */
    public int updateHaoGroup(HaoPrayGroup group) {
        if (TextUtils.isEmpty(group.getObjectId())) {
            throw new IllegalArgumentException("objectId is empty!");
        }
        SQLiteDatabase database = getDatabase();
        ContentValues values = new ContentValues();
        values.put("objectId", group.getObjectId());
        values.put("updatedAt", SQLiteUtils.getUpdatedAt());
        values.put("groupId", group.groupId);
        values.put("prayId", group.prayId);
        values.put("pray", group.getPray());
        return database.update("HaoGroup", values, "objectId=?", new String[]{group.getObjectId()});
    }

    /**
     * 根据objectId更新HaoGroup记录的指定字段
     * @param objectId 记录的objectId
     * @param fieldValues 要更新的字段和值的映射
     * @return 更新影响的行数
     */
    public int updateHaoGroupFields(String objectId, Map<String, Object> fieldValues) {
        if (TextUtils.isEmpty(objectId)) {
            throw new IllegalArgumentException("objectId is empty!");
        }
        if (fieldValues == null || fieldValues.isEmpty()) {
            throw new IllegalArgumentException("fieldValues is empty!");
        }

        SQLiteDatabase database = getDatabase();
        ContentValues values = new ContentValues();

        for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                values.put(key, (String) value);
            } else if (value instanceof Integer) {
                values.put(key, (Integer) value);
            } else if (value instanceof Long) {
                values.put(key, (Long) value);
            } else if (value instanceof Boolean) {
                values.put(key, (Boolean) value);
            } else if (value != null) {
                values.put(key, value.toString());
            }
        }

        // 自动更新updatedAt字段
        values.put("updatedAt", SQLiteUtils.getUpdatedAt());

        return database.update("HaoGroup", values, "objectId=?", new String[]{objectId});
    }

    /**
     * 同步更新分组信息，包括关联的HaoPray表数据
     * 当HaoPrayGroup的prayId发生变化时，同步更新HaoPray表中对应记录的id字段
     */
    public int updateHaoGroupWithSync(HaoPrayGroup group, String oldPrayId) {
        if (TextUtils.isEmpty(group.getObjectId())) {
            throw new IllegalArgumentException("objectId is empty!");
        }

        SQLiteDatabase database = getDatabase();
        database.beginTransaction();

        try {
            // 1. 更新HaoGroup表
            ContentValues groupValues = new ContentValues();
            groupValues.put("objectId", group.getObjectId());
            groupValues.put("updatedAt", SQLiteUtils.getUpdatedAt());
            groupValues.put("groupId", group.groupId);
            groupValues.put("prayId", group.prayId);
            groupValues.put("pray", group.getPray());
            int groupUpdateResult = database.update("HaoGroup", groupValues, "objectId=?", new String[]{group.getObjectId()});

            if (groupUpdateResult == 0) {
                throw new IllegalStateException("未找到要更新的分组记录");
            }

            // 2. 如果prayId发生了变化，同步更新HaoPray表
            if (!TextUtils.isEmpty(oldPrayId) && !oldPrayId.equals(group.prayId)) {
                ContentValues prayValues = new ContentValues();
                prayValues.put("id", group.prayId);
                prayValues.put("updatedAt", SQLiteUtils.getUpdatedAt());
                int prayUpdateResult = database.update("HaoPray", prayValues, "id=?", new String[]{oldPrayId});

                // 记录同步操作日志
                android.util.Log.d("SQLiteHelper", "Synced HaoPray records: " + prayUpdateResult + " rows updated from id=" + oldPrayId + " to id=" + group.prayId);
            }

            database.setTransactionSuccessful();
            return groupUpdateResult;

        } catch (Exception e) {
            android.util.Log.e("SQLiteHelper", "Error updating group with sync", e);
            throw e;
        } finally {
            database.endTransaction();
        }
    }

    /**
     * 查询指定objectId的HaoPrayGroup记录
     */
    public HaoPrayGroup queryHaoGroupByObjectId(String objectId) {
        if (TextUtils.isEmpty(objectId)) {
            return null;
        }

        SQLiteDatabase database = this.getDatabase();
        Cursor cursor = database.rawQuery("select * from HaoGroup where objectId=?", new String[]{objectId});
        CursorProxy proxy = new CursorProxy(cursor);

        HaoPrayGroup result = null;
        if (proxy.moveToNext()) {
            int groupId = proxy.getInt("groupId");
            String prayId = proxy.getString("prayId");
            String prayText = proxy.getString("pray");
            result = new HaoPrayGroup(groupId, prayId, prayText);
            result.setObjectId(proxy.getString("objectId"));
            result.setCreatedAt(proxy.getString("createdAt"));
            result.setUpdatedAt(proxy.getString("updatedAt"));
        }
        cursor.close();
        return result;
    }

    /**
     * 检测并合并具有相同GroupId和PrayId的重复记录
     * @param groupId 要检查的GroupId
     * @param prayId 要检查的PrayId
     * @return 合并的记录数量，0表示没有重复记录
     */
    public int detectAndMergeDuplicateRecords(int groupId, String prayId) {
        if (TextUtils.isEmpty(prayId)) {
            return 0;
        }

        SQLiteDatabase database = getDatabase();
        database.beginTransaction();

        try {
            // 1. 查找所有具有相同GroupId和PrayId的记录
            Cursor cursor = database.rawQuery(
                "SELECT * FROM HaoGroup WHERE groupId=? AND prayId=? ORDER BY createdAt ASC",
                new String[]{String.valueOf(groupId), prayId}
            );

            List<HaoPrayGroup> duplicateRecords = new ArrayList<>();
            CursorProxy proxy = new CursorProxy(cursor);

            while (proxy.moveToNext()) {
                HaoPrayGroup group = new HaoPrayGroup(
                    proxy.getInt("groupId"),
                    proxy.getString("prayId"),
                    proxy.getString("pray")
                );
                group.setObjectId(proxy.getString("objectId"));
                group.setCreatedAt(proxy.getString("createdAt"));
                group.setUpdatedAt(proxy.getString("updatedAt"));
                duplicateRecords.add(group);
            }
            cursor.close();

            // 2. 如果只有一条记录或没有记录，不需要合并
            if (duplicateRecords.size() <= 1) {
                database.setTransactionSuccessful();
                return 0;
            }

            // 3. 执行合并操作
            int mergedCount = mergeDuplicateRecords(database, duplicateRecords);

            database.setTransactionSuccessful();
            android.util.Log.d("SQLiteHelper", "Successfully merged " + mergedCount + " duplicate records for GroupId=" + groupId + ", PrayId=" + prayId);
            return mergedCount;

        } catch (Exception e) {
            android.util.Log.e("SQLiteHelper", "Error merging duplicate records", e);
            throw e;
        } finally {
            database.endTransaction();
        }
    }

    /**
     * 执行重复记录的合并操作
     * @param database 数据库实例
     * @param duplicateRecords 重复记录列表（按createdAt升序排列）
     * @return 被合并删除的记录数量
     */
    private int mergeDuplicateRecords(SQLiteDatabase database, List<HaoPrayGroup> duplicateRecords) {
        if (duplicateRecords.size() <= 1) {
            return 0;
        }

        // 1. 保留最早的记录作为主记录
        HaoPrayGroup primaryRecord = duplicateRecords.get(0);
        List<HaoPrayGroup> recordsToMerge = duplicateRecords.subList(1, duplicateRecords.size());

        // 2. 合并所有pray内容
        StringBuilder mergedPrayContent = new StringBuilder();
        if (!TextUtils.isEmpty(primaryRecord.getPray())) {
            mergedPrayContent.append(primaryRecord.getPray());
        }

        for (HaoPrayGroup record : recordsToMerge) {
            if (!TextUtils.isEmpty(record.getPray())) {
                if (mergedPrayContent.length() > 0) {
                    mergedPrayContent.append(";");
                }
                mergedPrayContent.append(record.getPray());
            }
        }

        // 3. 更新主记录的内容
        ContentValues updateValues = new ContentValues();
        updateValues.put("pray", mergedPrayContent.toString());
        updateValues.put("updatedAt", SQLiteUtils.getUpdatedAt());

        int updateResult = database.update("HaoGroup", updateValues, "objectId=?",
            new String[]{primaryRecord.getObjectId()});

        if (updateResult == 0) {
            throw new IllegalStateException("Failed to update primary record during merge");
        }

        // 4. 删除其他重复记录
        int deletedCount = 0;
        for (HaoPrayGroup record : recordsToMerge) {
            int deleteResult = database.delete("HaoGroup", "objectId=?",
                new String[]{record.getObjectId()});
            if (deleteResult > 0) {
                deletedCount++;
                android.util.Log.d("SQLiteHelper", "Deleted duplicate record with objectId: " + record.getObjectId());
            }
        }

        android.util.Log.d("SQLiteHelper", "Merged pray content: " + mergedPrayContent.toString());
        android.util.Log.d("SQLiteHelper", "Primary record objectId: " + primaryRecord.getObjectId());

        return deletedCount;
    }

    /**
     * 检测并合并指定记录更新后可能产生的重复记录
     * 这个方法在记录更新后调用，用于自动处理重复记录
     * @param updatedGroup 刚更新的记录
     * @return 合并结果信息
     */
    public MergeResult detectAndMergeAfterUpdate(HaoPrayGroup updatedGroup) {
        if (updatedGroup == null || TextUtils.isEmpty(updatedGroup.getObjectId())) {
            return new MergeResult(false, 0, "无效的更新记录");
        }

        try {
            int mergedCount = detectAndMergeDuplicateRecords(updatedGroup.groupId, updatedGroup.prayId);
            if (mergedCount > 0) {
                return new MergeResult(true, mergedCount, "检测到重复记录已自动合并");
            } else {
                return new MergeResult(false, 0, null);
            }
        } catch (Exception e) {
            android.util.Log.e("SQLiteHelper", "Error in detectAndMergeAfterUpdate", e);
            return new MergeResult(false, 0, "合并过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 合并结果类
     */
    public static class MergeResult {
        public final boolean hasMerged;
        public final int mergedCount;
        public final String message;

        public MergeResult(boolean hasMerged, int mergedCount, String message) {
            this.hasMerged = hasMerged;
            this.mergedCount = mergedCount;
            this.message = message;
        }
    }

    /**
     * 按日期归类记录数量
     */
    public List<DateBean> groupByDate() {
        SQLiteDatabase database = SQLiteHelper.getInstance().getDatabase();
        String sql = "SELECT date, COUNT(*) AS count FROM HaoPray GROUP BY date ORDER BY date DESC LIMIT 100;";
        Cursor cursor = database.rawQuery(sql, null);
        CursorProxy proxy = new CursorProxy(cursor);
        ArrayList<DateBean> results = new ArrayList<>();
        while (proxy.moveToNext()) {
            DateBean bean = new DateBean();
            bean.date = proxy.getString("date");
            bean.count = proxy.getString("count");
            results.add(bean);
        }
        cursor.close();
        return results;
    }

    // ==================== 数据库管理功能相关方法 ====================

    /**
     * 获取数据库中所有表的信息
     * @return 表信息列表，包含表名和记录数量
     */
    public List<Map<String, Object>> getTableInfoList() {
        SQLiteDatabase database = this.getDatabase();
        List<Map<String, Object>> tableInfoList = new ArrayList<>();

        // 获取HaoPray表信息
        Map<String, Object> haoPrayInfo = new HashMap<>();
        haoPrayInfo.put("tableName", "HaoPray");
        haoPrayInfo.put("recordCount", getTableRecordCount("HaoPray"));
        tableInfoList.add(haoPrayInfo);

        // 获取HaoGroup表信息
        Map<String, Object> haoGroupInfo = new HashMap<>();
        haoGroupInfo.put("tableName", "HaoGroup");
        haoGroupInfo.put("recordCount", getTableRecordCount("HaoGroup"));
        tableInfoList.add(haoGroupInfo);

        return tableInfoList;
    }

    /**
     * 获取指定表的记录数量
     * @param tableName 表名
     * @return 记录数量
     */
    public int getTableRecordCount(String tableName) {
        SQLiteDatabase database = this.getDatabase();
        Cursor cursor = database.rawQuery("SELECT COUNT(*) FROM " + tableName, null);
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        cursor.close();
        return count;
    }

    /**
     * 获取指定表的所有字段信息
     * @param tableName 表名
     * @return 字段信息列表
     */
    public List<String> getTableColumns(String tableName) {
        SQLiteDatabase database = this.getDatabase();
        List<String> columns = new ArrayList<>();
        Cursor cursor = database.rawQuery("PRAGMA table_info(" + tableName + ")", null);
        while (cursor.moveToNext()) {
            String columnName = cursor.getString(cursor.getColumnIndex("name"));
            columns.add(columnName);
        }
        cursor.close();
        return columns;
    }

    /**
     * 在指定表中进行模糊搜索
     * @param tableName 表名
     * @param keyword 搜索关键词
     * @param limit 限制返回的记录数量
     * @return 搜索结果列表
     */
    public List<Map<String, Object>> searchInTable(String tableName, String keyword, String searchField, int limit) {
        return searchInTable(tableName, keyword, searchField, limit, 0);
    }

    /**
     * 在指定表中进行模糊搜索（支持分页）
     * @param tableName 表名
     * @param keyword 搜索关键词
     * @param limit 限制返回的记录数量
     * @param offset 偏移量（用于分页）
     * @return 搜索结果列表
     */
    public List<Map<String, Object>> searchInTable(String tableName, String keyword, int limit, int offset) {
        return searchInTable(tableName, keyword, "All", limit, offset);
    }

    /**
     * 在指定表中进行模糊搜索（支持分页和指定字段）
     * @param tableName 表名
     * @param keyword 搜索关键词
     * @param searchField 要搜索的字段，如果为 “All”，则搜索所有字段
     * @param limit 限制返回的记录数量
     * @param offset 偏移量（用于分页）
     * @return 搜索结果列表
     */
    public List<Map<String, Object>> searchInTable(String tableName, String keyword, String searchField, int limit, int offset) {
        SQLiteDatabase database = this.getDatabase();
        List<Map<String, Object>> results = new ArrayList<>();

        if (TextUtils.isEmpty(keyword)) {
            // 如果关键词为空，返回最新的记录
            return getLatestRecords(tableName, limit, offset);
        }

        // 构建搜索SQL
        StringBuilder sql = new StringBuilder("SELECT * FROM " + tableName + " WHERE ");
        List<String> columns = getTableColumns(tableName);
        List<String> conditions = new ArrayList<>();
        List<String> argsList = new ArrayList<>();

        // 对关键词进行Base16编码，用于搜索编码后的内容
        String encodedKeyword = "";
        try {
            encodedKeyword = com.haoxueren.utils.Base16.encode(keyword);
        } catch (Exception e) {
            // 编码失败时使用空字符串
            encodedKeyword = "";
        }

        if ("All".equalsIgnoreCase(searchField) || TextUtils.isEmpty(searchField)) {
            for (String column : columns) {
                if ("pray".equals(column)) {
                    // 对于pray字段，同时搜索原始内容和编码后的内容
                    if (!TextUtils.isEmpty(encodedKeyword)) {
                        conditions.add("(" + column + " LIKE ? OR " + column + " LIKE ?)");
                        argsList.add("%" + keyword + "%");        // 搜索原始内容
                        argsList.add("%" + encodedKeyword + "%"); // 搜索编码后的内容
                    } else {
                        conditions.add(column + " LIKE ?");
                        argsList.add("%" + keyword + "%");
                    }
                } else {
                    // 对于其他字段进行普通模糊搜索
                    conditions.add(column + " LIKE ?");
                    argsList.add("%" + keyword + "%");
                }
            }
            sql.append(String.join(" OR ", conditions));
        } else {
            if ("pray".equals(searchField)) {
                if (!TextUtils.isEmpty(encodedKeyword)) {
                    sql.append("(" + searchField + " LIKE ? OR " + searchField + " LIKE ?)");
                    argsList.add("%" + keyword + "%");        // 搜索原始内容
                    argsList.add("%" + encodedKeyword + "%"); // 搜索编码后的内容
                } else {
                    sql.append(searchField + " LIKE ?");
                    argsList.add("%" + keyword + "%");
                }
            } else {
                sql.append(searchField + " LIKE ?");
                argsList.add("%" + keyword + "%");
            }
        }

        sql.append(" ORDER BY ");

        // 根据表名添加适当的排序
        if ("HaoPray".equals(tableName)) {
            sql.append("date DESC, createdAt DESC");
        } else if ("HaoGroup".equals(tableName)) {
            sql.append("groupId ASC, createdAt DESC");
        } else {
            sql.append("rowid DESC");
        }

        sql.append(" LIMIT ? OFFSET ?");

        // 准备参数
        argsList.add(String.valueOf(limit));
        argsList.add(String.valueOf(offset));

        String[] args = argsList.toArray(new String[0]);

        Cursor cursor = database.rawQuery(sql.toString(), args);
        CursorProxy proxy = new CursorProxy(cursor);

        while (proxy.moveToNext()) {
            Map<String, Object> record = new HashMap<>();
            for (String column : columns) {
                record.put(column, proxy.getString(column));
            }
            results.add(record);
        }
        cursor.close();

        return results;
    }

    /**
     * 获取在指定表中搜索的结果数量
     * @param tableName 表名
     * @param keyword 搜索关键词
     * @return 搜索结果的数量
     */
    public int searchInTableCount(String tableName, String keyword) {
        return searchInTableCount(tableName, keyword, "All");
    }

    /**
     * 获取在指定表中搜索的结果数量（支持指定字段）
     * @param tableName 表名
     * @param keyword 搜索关键词
     * @param searchField 要搜索的字段，如果为 “All”，则搜索所有字段
     * @return 搜索结果的数量
     */
    public int searchInTableCount(String tableName, String keyword, String searchField) {
        SQLiteDatabase database = this.getDatabase();

        if (TextUtils.isEmpty(keyword)) {
            return getTableRecordCount(tableName);
        }

        // 构建搜索SQL
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM " + tableName + " WHERE ");
        List<String> columns = getTableColumns(tableName);
        List<String> conditions = new ArrayList<>();
        List<String> argsList = new ArrayList<>();

        // 对关键词进行Base16编码，用于搜索编码后的内容
        String encodedKeyword = "";
        try {
            encodedKeyword = com.haoxueren.utils.Base16.encode(keyword);
        } catch (Exception e) {
            // 编码失败时使用空字符串
            encodedKeyword = "";
        }

        if ("All".equalsIgnoreCase(searchField) || TextUtils.isEmpty(searchField)) {
            for (String column : columns) {
                if ("pray".equals(column)) {
                    // 对于pray字段，同时搜索原始内容和编码后的内容
                    if (!TextUtils.isEmpty(encodedKeyword)) {
                        conditions.add("(" + column + " LIKE ? OR " + column + " LIKE ?)");
                        argsList.add("%" + keyword + "%");        // 搜索原始内容
                        argsList.add("%" + encodedKeyword + "%"); // 搜索编码后的内容
                    } else {
                        conditions.add(column + " LIKE ?");
                        argsList.add("%" + keyword + "%");
                    }
                } else {
                    // 对于其他字段进行普通模糊搜索
                    conditions.add(column + " LIKE ?");
                    argsList.add("%" + keyword + "%");
                }
            }
            sql.append(String.join(" OR ", conditions));
        } else {
            if ("pray".equals(searchField)) {
                if (!TextUtils.isEmpty(encodedKeyword)) {
                    sql.append("(" + searchField + " LIKE ? OR " + searchField + " LIKE ?)");
                    argsList.add("%" + keyword + "%");        // 搜索原始内容
                    argsList.add("%" + encodedKeyword + "%"); // 搜索编码后的内容
                } else {
                    sql.append(searchField + " LIKE ?");
                    argsList.add("%" + keyword + "%");
                }
            } else {
                sql.append(searchField + " LIKE ?");
                argsList.add("%" + keyword + "%");
            }
        }

        String[] args = argsList.toArray(new String[0]);

        Cursor cursor = database.rawQuery(sql.toString(), args);
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        cursor.close();

        return count;
    }

    /**
     * 获取指定表的最新记录
     * @param tableName 表名
     * @param limit 限制返回的记录数量
     * @return 最新记录列表
     */
    public List<Map<String, Object>> getLatestRecords(String tableName, int limit) {
        return getLatestRecords(tableName, limit, 0);
    }

    /**
     * 获取指定表的最新记录（支持分页）
     * @param tableName 表名
     * @param limit 限制返回的记录数量
     * @param offset 偏移量（用于分页）
     * @return 最新记录列表
     */
    public List<Map<String, Object>> getLatestRecords(String tableName, int limit, int offset) {
        SQLiteDatabase database = this.getDatabase();
        List<Map<String, Object>> results = new ArrayList<>();

        String sql = "SELECT * FROM " + tableName + " ORDER BY ";

        // 根据表名添加适当的排序
        if ("HaoPray".equals(tableName)) {
            sql += "date DESC, createdAt DESC";
        } else if ("HaoGroup".equals(tableName)) {
            sql += "groupId ASC, createdAt DESC";
        } else {
            sql += "rowid DESC";
        }

        sql += " LIMIT ? OFFSET ?";

        Cursor cursor = database.rawQuery(sql, new String[]{String.valueOf(limit), String.valueOf(offset)});
        List<String> columns = getTableColumns(tableName);
        CursorProxy proxy = new CursorProxy(cursor);

        while (proxy.moveToNext()) {
            Map<String, Object> record = new HashMap<>();
            for (String column : columns) {
                record.put(column, proxy.getString(column));
            }
            results.add(record);
        }
        cursor.close();

        return results;
    }


}
