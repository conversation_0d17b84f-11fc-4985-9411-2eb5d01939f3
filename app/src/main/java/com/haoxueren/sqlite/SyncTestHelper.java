package com.haoxueren.sqlite;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.haoxueren.pray.MyApplication;
import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.group.HaoPrayGroup;
import com.haoxueren.utils.DateUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 数据库同步测试辅助工具
 * 用于生成测试数据、验证同步结果、性能测试等
 */
public class SyncTestHelper {

    private static final String[] TEST_PRAYERS = {
        "主啊，求你赐给我智慧和力量",
        "感谢主的恩典和保守",
        "求主带领我前面的道路",
        "愿主的旨意成就在我身上",
        "主啊，求你医治我的心灵",
        "感谢主赐给我平安喜乐",
        "求主保守我的家人平安",
        "愿主的爱充满我的心",
        "主啊，求你指引我的方向",
        "感谢主每天的供应和看顾"
    };

    private static final String[] TEST_GROUPS = {
        "晨祷", "晚祷", "感恩祷告", "代祷", "悔改祷告",
        "赞美祷告", "祈求祷告", "默想祷告", "团契祷告", "个人祷告"
    };

    /**
     * 生成测试数据
     */
    public static Observable<String> generateTestData(int prayCount, int groupCount) {
        return Observable.<String>create(emitter -> {
            try {
                SQLiteHelper helper = SQLiteHelper.getInstance();
                Random random = new Random();
                
                emitter.onNext("开始生成测试数据...");
                
                // 生成祷告记录
                for (int i = 0; i < prayCount; i++) {
                    HaoPray pray = new HaoPray();
                    pray.setId("test_" + i);
                    pray.setDate(DateUtils.today());
                    pray.setCount(String.valueOf(random.nextInt(10) + 1));
                    pray.setPray(TEST_PRAYERS[random.nextInt(TEST_PRAYERS.length)]);
                    
                    helper.insertHaoPray(pray).subscribe();
                    
                    if (i % 100 == 0) {
                        emitter.onNext("已生成祷告记录: " + i + "/" + prayCount);
                    }
                }
                
                // 生成分组记录
                for (int i = 0; i < groupCount; i++) {
                    String groupName = TEST_GROUPS[random.nextInt(TEST_GROUPS.length)];
                    String prayId = "test_" + random.nextInt(prayCount);
                    String prayText = TEST_PRAYERS[random.nextInt(TEST_PRAYERS.length)];
                    
                    helper.insertHaoGroup(i + 1, prayId, prayText);
                    
                    if (i % 50 == 0) {
                        emitter.onNext("已生成分组记录: " + i + "/" + groupCount);
                    }
                }
                
                emitter.onNext("测试数据生成完成！");
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 清理测试数据
     */
    public static Observable<String> cleanTestData() {
        return Observable.<String>create(emitter -> {
            try {
                SQLiteDatabase db = SQLiteHelper.getInstance().getDatabase();
                
                emitter.onNext("正在清理测试数据...");
                
                // 删除测试祷告记录
                int prayDeleted = db.delete("HaoPray", "id LIKE ?", new String[]{"test_%"});
                emitter.onNext("删除了 " + prayDeleted + " 条测试祷告记录");
                
                // 删除测试分组记录
                int groupDeleted = db.delete("HaoGroup", "prayId LIKE ?", new String[]{"test_%"});
                emitter.onNext("删除了 " + groupDeleted + " 条测试分组记录");
                
                emitter.onNext("测试数据清理完成！");
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 验证数据库完整性
     */
    public static Observable<DatabaseIntegrityResult> verifyDatabaseIntegrity() {
        return Observable.<DatabaseIntegrityResult>create(emitter -> {
            try {
                DatabaseIntegrityResult result = new DatabaseIntegrityResult();
                SQLiteDatabase db = SQLiteHelper.getInstance().getDatabase();
                
                // 检查表结构
                Cursor cursor = db.rawQuery("SELECT name FROM sqlite_master WHERE type='table'", null);
                List<String> tables = new ArrayList<>();
                while (cursor.moveToNext()) {
                    tables.add(cursor.getString(0));
                }
                cursor.close();
                
                result.hasHaoPrayTable = tables.contains("HaoPray");
                result.hasHaoGroupTable = tables.contains("HaoGroup");
                
                // 检查记录数量
                cursor = db.rawQuery("SELECT COUNT(*) FROM HaoPray", null);
                if (cursor.moveToFirst()) {
                    result.prayRecordCount = cursor.getInt(0);
                }
                cursor.close();
                
                cursor = db.rawQuery("SELECT COUNT(*) FROM HaoGroup", null);
                if (cursor.moveToFirst()) {
                    result.groupRecordCount = cursor.getInt(0);
                }
                cursor.close();
                
                // 检查数据一致性
                cursor = db.rawQuery("SELECT COUNT(DISTINCT id) FROM HaoPray", null);
                if (cursor.moveToFirst()) {
                    result.uniquePrayIds = cursor.getInt(0);
                }
                cursor.close();
                
                cursor = db.rawQuery("SELECT COUNT(DISTINCT groupId) FROM HaoGroup", null);
                if (cursor.moveToFirst()) {
                    result.uniqueGroupIds = cursor.getInt(0);
                }
                cursor.close();
                
                // 检查数据库文件大小
                Context context = MyApplication.getContext();
                File dbFile = context.getDatabasePath("HaoPray.db");
                result.databaseSize = dbFile.length();
                
                result.isValid = result.hasHaoPrayTable && result.hasHaoGroupTable && 
                               result.prayRecordCount >= 0 && result.groupRecordCount >= 0;
                
                emitter.onNext(result);
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 性能测试 - 测试同步速度
     */
    public static class PerformanceTest {
        public long startTime;
        public long endTime;
        public long duration;
        public long dataSize;
        public double transferRate; // KB/s
        
        public void start() {
            startTime = System.currentTimeMillis();
        }
        
        public void end(long dataSize) {
            endTime = System.currentTimeMillis();
            duration = endTime - startTime;
            this.dataSize = dataSize;
            
            if (duration > 0) {
                transferRate = (dataSize / 1024.0) / (duration / 1000.0);
            }
        }
        
        public String getReport() {
            return String.format(
                "性能测试报告:\n" +
                "数据大小: %.1f KB\n" +
                "传输时间: %d ms\n" +
                "传输速度: %.1f KB/s",
                dataSize / 1024.0, duration, transferRate
            );
        }
    }

    /**
     * 数据库完整性检查结果
     */
    public static class DatabaseIntegrityResult {
        public boolean isValid;
        public boolean hasHaoPrayTable;
        public boolean hasHaoGroupTable;
        public int prayRecordCount;
        public int groupRecordCount;
        public int uniquePrayIds;
        public int uniqueGroupIds;
        public long databaseSize;
        
        public String getReport() {
            return String.format(
                "数据库完整性检查报告:\n" +
                "状态: %s\n" +
                "HaoPray表: %s\n" +
                "HaoGroup表: %s\n" +
                "祷告记录数: %d\n" +
                "分组记录数: %d\n" +
                "唯一祷告ID数: %d\n" +
                "唯一分组ID数: %d\n" +
                "数据库大小: %.1f KB",
                isValid ? "正常" : "异常",
                hasHaoPrayTable ? "存在" : "缺失",
                hasHaoGroupTable ? "存在" : "缺失",
                prayRecordCount, groupRecordCount,
                uniquePrayIds, uniqueGroupIds,
                databaseSize / 1024.0
            );
        }
    }

    /**
     * 模拟网络延迟测试
     */
    public static Observable<String> simulateNetworkDelay(int delayMs) {
        return Observable.<String>create(emitter -> {
            try {
                emitter.onNext("模拟网络延迟: " + delayMs + "ms");
                Thread.sleep(delayMs);
                emitter.onNext("延迟模拟完成");
                emitter.onComplete();
            } catch (InterruptedException e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 压力测试 - 连续多次同步
     */
    public static Observable<String> stressTest(DatabaseSyncManager.DeviceInfo device, 
                                              int iterations, 
                                              EnhancedSocketManager.SyncProgressCallback callback) {
        return Observable.<String>create(emitter -> {
            int successCount = 0;
            int failureCount = 0;
            
            for (int i = 0; i < iterations; i++) {
                emitter.onNext("开始第 " + (i + 1) + " 次同步测试...");
                
                try {
                    // 这里应该调用实际的同步方法
                    // 为了简化，我们只是模拟
                    Thread.sleep(1000); // 模拟同步时间
                    successCount++;
                    emitter.onNext("第 " + (i + 1) + " 次同步成功");
                } catch (Exception e) {
                    failureCount++;
                    emitter.onNext("第 " + (i + 1) + " 次同步失败: " + e.getMessage());
                }
            }
            
            emitter.onNext(String.format("压力测试完成！成功: %d, 失败: %d", successCount, failureCount));
            emitter.onComplete();
            
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());
    }
}
