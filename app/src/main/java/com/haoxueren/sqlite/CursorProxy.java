package com.haoxueren.sqlite;

import android.database.Cursor;

/**
 * SQLite Cursor的代理类；
 */
public class CursorProxy {

    private Cursor cursor;

    public CursorProxy(Cursor cursor) {
        this.cursor = cursor;
    }

    public boolean moveToNext() {
        return cursor.moveToNext();
    }

    /**
     * 获取String类型的内容
     */
    public String getString(String columnName) {
        int index = cursor.getColumnIndex(columnName);
        if (index < 0) {
            throw new IllegalArgumentException("column " + columnName + "not exist!");
        }
        return cursor.getString(index);
    }

    /**
     * 获取int类型的内容
     */
    public int getInt(String columnName) {
        int index = cursor.getColumnIndex(columnName);
        if (index < 0) {
            throw new IllegalArgumentException("column " + columnName + "not exist!");
        }
        return cursor.getInt(index);
    }
}
