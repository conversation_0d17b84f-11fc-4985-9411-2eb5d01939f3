package com.haoxueren.sqlite;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.haoxueren.pray.MyApplication;
import com.haoxueren.proxy.InputStreamProxy;
import com.haoxueren.proxy.OutputStreamProxy;
import com.haoxueren.restful.GsonUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * Socket服务端和客户端管理
 */
public class SocketManager {

    public static boolean running = true;

    public static void startServer(int port) {
        new Thread() {
            @Override
            public void run() {
                try {
                    Gson gson = GsonUtils.getGson();
                    ServerSocket server = new ServerSocket(port);
                    while (running) try {
                        Socket socket = server.accept();
                        InputStream inputStream = socket.getInputStream();
                        InputStreamProxy inputStreamProxy = new InputStreamProxy(inputStream);
                        String result = inputStreamProxy.readString();
                        socket.shutdownInput();
                        // 收到消息，根据类型分别处理
                        OutputStream outputStream = socket.getOutputStream();
                        OutputStreamProxy outputStreamProxy = new OutputStreamProxy(outputStream);
                        JsonObject json = gson.fromJson(result, JsonObject.class);
                        String action = json.get("action").getAsString();
                        if ("action_sync_database".equals(action)) {
                            Context context = MyApplication.getContext();
                            File database = context.getDatabasePath("HaoPray.db");
                            outputStreamProxy.write(database);
                        } else {
                            outputStreamProxy.write("unknown action");
                        }
                        socket.shutdownOutput();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }.start();
    }


    /**
     * 向远程主机发送消息
     */
    public static Observable<String> syncDatabase(String serverIp, int port) {
        return Observable.<String>create(emitter -> {
            emitter.onNext("正在连接：" + serverIp + ":" + port);
            Socket socket = new Socket(serverIp, port);
            OutputStream outputStream = socket.getOutputStream();
            OutputStreamProxy outputStreamProxy = new OutputStreamProxy(outputStream);
            JsonObject params = new JsonObject();
            params.addProperty("action", "action_sync_database");
            outputStreamProxy.write(params.toString());
            emitter.onNext("开始下载：" + params);
            socket.shutdownOutput();
            InputStream inputStream = socket.getInputStream();
            InputStreamProxy inputStreamProxy = new InputStreamProxy(inputStream);
            Context context = MyApplication.getContext();
            File download = context.getDatabasePath("HaoPray_temp.db");
            inputStreamProxy.readFile(download, progress -> {
                emitter.onNext("正在下载：" + progress / 1024 + " KB");
            });
            emitter.onNext("下载成功：" + download.getAbsolutePath());
            File database = context.getDatabasePath("HaoPray.db");
            boolean success = download.renameTo(database);
            if (success) {
                emitter.onNext("同步成功：" + database.length() / 1024 + "KB");
            } else {
                emitter.onNext("同步失败：" + download.length() / 1024 + "KB");
            }
        }).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread());

    }


}
