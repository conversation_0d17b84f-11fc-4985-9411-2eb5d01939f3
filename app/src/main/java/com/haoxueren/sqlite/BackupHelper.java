package com.haoxueren.sqlite;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Environment;

import com.haoxueren.utils.DateUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;

import io.reactivex.Observable;

/**
 * 数据备份帮助类
 */
@SuppressLint("CheckResult")
public class BackupHelper {

    /**
     * 备份SQLite数据库
     */
    public static Observable<File> backupSQLite(Context context) {
        return Observable.create(emitter -> {
            SQLiteHelper.getInstance().close();
            File database = context.getDatabasePath("HaoPray.db");
            if (!database.exists()) {
                emitter.onError(new FileNotFoundException("HaoPray.db not found"));
                return;
            }
            File root = Environment.getExternalStorageDirectory();
            File download = new File(root, "Download/Database");
            if (!download.exists()) {
                download.mkdirs();
            }
            String time = DateUtils.today("yyyyMMdd");
            File backupFile = new File(download, "HaoPray_" + time + ".db");
            try (FileInputStream in = new FileInputStream(database);
                 RandomAccessFile out = new RandomAccessFile(backupFile, "rw");
                 FileChannel inChannel = in.getChannel();
                 FileChannel outChannel = out.getChannel()) {
                outChannel.transferFrom(inChannel, 0, inChannel.size());
                emitter.onNext(backupFile);
            } catch (Exception e) {
                e.printStackTrace();
                emitter.onError(e);
            }
        });
    }
}
