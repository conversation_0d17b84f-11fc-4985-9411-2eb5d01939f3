package com.haoxueren.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.haoxueren.pray.R;
import com.haoxueren.pray.log.MyLog;

public class StatefulLayout extends FrameLayout {

    private View loadingLayout, emptyLayout, failureLayout;

    private Helper helper;

    public StatefulLayout(Context context) {
        super(context, null);
    }

    public StatefulLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public StatefulLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        helper = new Helper(this);
        LayoutInflater inflater = LayoutInflater.from(context);
        loadingLayout = inflater.inflate(R.layout.layout_loading, this, false);
        failureLayout = inflater.inflate(R.layout.layout_error, this, false);
        emptyLayout = inflater.inflate(R.layout.layout_empty, this, false);
    }

    /**
     * 加载中...
     */
    public void onLoading() {
        helper.removeAllStateView();
        loadingLayout.setBackgroundResource(R.color.theme);
        this.addView(loadingLayout);
    }

    /**
     * 加载中...
     */
    public void onLoading(int backgroundColor) {
        helper.removeAllStateView();
        loadingLayout.setBackgroundColor(backgroundColor);
        this.addView(loadingLayout);
    }


    public void onSuccess() {
        helper.removeAllStateView();
    }

    public void onFinish() {
        helper.removeAllStateView();
        MyLog.info("onFinish...");
    }

    public void onFailure(Throwable e) {
        helper.removeAllStateView();
        TextView failureTextView = failureLayout.findViewById(R.id.failureTextView);
        failureTextView.setText(e == null ? "加载失败" : e.getMessage());
        this.addView(failureLayout);
    }

    public void onEmpty() {
        helper.removeAllStateView();
        this.addView(emptyLayout);
    }

    class Helper {

        private final StatefulLayout layout;

        Helper(StatefulLayout layout) {
            this.layout = layout;
        }

        private void removeAllStateView() {
            layout.removeView(loadingLayout);
            layout.removeView(failureLayout);
            layout.removeView(emptyLayout);
        }
    }


}
