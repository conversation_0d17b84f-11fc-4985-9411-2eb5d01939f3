package com.haoxueren.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.haoxueren.pray.R;


public class TitleEditText extends FrameLayout implements TextWatcher {

    private EditText editText;
    private TextView textView;
    private OnTextChangedListener onTextChangedListener;

    public TitleEditText(Context context) {
        this(context, null);
    }

    public TitleEditText(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        View.inflate(context, R.layout.widget_title_edittext, this);
        // 获取自定义的属性
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.TitleEditText);
        String title = typedArray.getString(R.styleable.TitleEditText_android_title);
        String hint = typedArray.getString(R.styleable.TitleEditText_android_hint);
        String text = typedArray.getString(R.styleable.TitleEditText_android_text);
        int gravity = typedArray.getInt(R.styleable.TitleEditText_android_gravity, 0);
        boolean enabled = typedArray.getBoolean(R.styleable.TitleEditText_android_enabled, true);
        int inputType = typedArray.getInt(R.styleable.TitleEditText_android_inputType, InputType.TYPE_CLASS_TEXT);
        int orientation = typedArray.getInt(R.styleable.TitleEditText_android_orientation, LinearLayout.HORIZONTAL);
        typedArray.recycle();
        // 初始化控件
        textView = this.findViewById(R.id.textView);
        editText = this.findViewById(R.id.editText);
        LinearLayout linearLayout = this.findViewById(R.id.linearLayout);
        textView.setText(title);
        editText.setHint(hint);
        editText.setText(text);
        editText.setGravity(gravity);
        editText.setEnabled(enabled);
        if (inputType == InputType.TYPE_CLASS_NUMBER) {
            editText.setInputType(inputType | InputType.TYPE_NUMBER_FLAG_SIGNED);
        } else {
            editText.setInputType(inputType);
        }
        linearLayout.setOrientation(orientation);
        // 初始化监听器
        editText.addTextChangedListener(this);
    }

    public String getText() {
        return editText.getText().toString();
    }

    public float getFloat() {
        String text = editText.getText().toString();
        if (TextUtils.isEmpty(text)) {
            return 0f;
        }
        return Float.parseFloat(text);
    }

    public void setText(String text) {
        editText.setText(text);
    }

    /**
     * 文本变化监听器
     */
    public void setOnTextChangedListener(OnTextChangedListener listener) {
        this.onTextChangedListener = listener;
    }

    @Override
    public void setOnFocusChangeListener(OnFocusChangeListener l) {
        editText.setOnFocusChangeListener(l);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (onTextChangedListener != null) {
            onTextChangedListener.onTextChanged(s.toString());
        }
    }

    public static interface OnTextChangedListener {
        void onTextChanged(String text);
    }
}
