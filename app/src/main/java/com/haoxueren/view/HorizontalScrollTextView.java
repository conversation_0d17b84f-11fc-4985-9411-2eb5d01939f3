package com.haoxueren.view;

import android.content.Context;
import android.graphics.Color;
import android.text.InputType;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;

/**
 * 支持水平滑动的TextView
 */
public class HorizontalScrollTextView extends AppCompatEditText {

    public HorizontalScrollTextView(@NonNull Context context) {
        this(context, null);
    }

    public HorizontalScrollTextView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.setFocusable(false);
        this.setFocusableInTouchMode(false);
        this.setMaxLines(1);
        this.setInputType(InputType.TYPE_CLASS_TEXT);
        this.setBackgroundColor(Color.TRANSPARENT);
        this.setCustomSelectionActionModeCallback(null);
    }

    private float downX, downY;

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        // 禁止拦截横向滑动事件
        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                downX = event.getX();
                downY = event.getY();
                break;
            case MotionEvent.ACTION_MOVE:
                float distanceX = Math.abs(event.getX() - downX);
                float distanceY = Math.abs(event.getY() - downY);
                if (distanceX > distanceY) {
                    this.getParent().requestDisallowInterceptTouchEvent(true);
                }
                break;
        }
        return super.dispatchTouchEvent(event);
    }
}
