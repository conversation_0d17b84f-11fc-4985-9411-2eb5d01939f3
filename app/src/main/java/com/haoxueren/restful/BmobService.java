package com.haoxueren.restful;

import com.google.gson.JsonObject;

import java.util.Locale;

import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public abstract class BmobService {

    protected abstract String getApplicationId();

    protected abstract String getRestApiKey();

    protected abstract String getTableName();

    protected abstract String getJsonBody(JsonObject bean);

    public Observable<String> save(JsonObject bean) {
        String tableName = this.getTableName();
        MediaType contentType = MediaType.parse("application/json");
        String json = this.getJsonBody(bean);
        RequestBody body = RequestBody.create(contentType, json);
        Request request = new Request.Builder()
                .url("https://api2.bmob.cn/1/classes/" + tableName)
                .addHeader("X-Bmob-Application-Id", getApplicationId())
                .addHeader("X-Bmob-REST-API-Key", getRestApiKey())
                .post(body)
                .build();
        return Observable.<String>create(emitter -> {
            try {
                OkHttpClient client = OkHttpManager.getClient();
                Response response = client.newCall(request).execute();
                String result = response.body().string();
                emitter.onNext(result);
            } catch (Exception e) {
                emitter.onError(e);
            }
        })
                .retry(3)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());

    }

    public Observable<String> delete(String objectId) {
        String tableName = this.getTableName();
        String url = String.format("https://api2.bmob.cn/1/classes/%s/%s", tableName, objectId);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("X-Bmob-Application-Id", getApplicationId())
                .addHeader("X-Bmob-REST-API-Key", getRestApiKey())
                .delete()
                .build();
        return Observable.<String>create(emitter -> {
            try {
                OkHttpClient client = OkHttpManager.getClient();
                Response response = client.newCall(request).execute();
                String result = response.body().string();
                emitter.onNext(result);
            } catch (Exception e) {
                emitter.onError(e);
            }
        })
                .retry(3)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    public Observable<String> update(String objectId, JsonObject bean) {
        String tableName = this.getTableName();
        MediaType contentType = MediaType.parse("application/json");
        String json = this.getJsonBody(bean);
        RequestBody body = RequestBody.create(contentType, json);
        String url = String.format("https://api2.bmob.cn/1/classes/%s/%s", tableName, objectId);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("X-Bmob-Application-Id", getApplicationId())
                .addHeader("X-Bmob-REST-API-Key", getRestApiKey())
                .put(body)
                .build();
        return Observable.<String>create(emitter -> {
            try {
                OkHttpClient client = OkHttpManager.getClient();
                Response response = client.newCall(request).execute();
                String result = response.body().string();
                emitter.onNext(result);
            } catch (Exception e) {
                emitter.onError(e);
            }
        })
                .retry(3)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }


    protected Observable<String> query(String condition) {
        String table = this.getTableName();
        String url = String.format(Locale.CHINA,
                "https://api2.bmob.cn/1/classes/%1$s?%2$s", table, condition);
        return sendGetRequest(url);
    }

    private Observable<String> sendGetRequest(String url) {
        Request request = new Request.Builder()
                .url(url)
                .addHeader("X-Bmob-Application-Id", getApplicationId())
                .addHeader("X-Bmob-REST-API-Key", getRestApiKey())
                .get()
                .build();
        return Observable.<String>create(
                emitter -> {
                    try {
                        OkHttpClient client = OkHttpManager.getClient();
                        Response response = client.newCall(request).execute();
                        String result = response.body().string();
                        emitter.onNext(result);
                    } catch (Exception e) {
                        emitter.onError(e);
                    }
                })
                .retry(3)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }
}
