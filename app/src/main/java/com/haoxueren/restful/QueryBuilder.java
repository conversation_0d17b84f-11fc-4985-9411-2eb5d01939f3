package com.haoxueren.restful;

import android.text.TextUtils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

public class QueryBuilder {

    private JsonObject where;

    private Integer skip, limit;

    private String order;

    private String keys;

    private String groupby;

    private Integer count;

    private Boolean groupcount;

    private QueryBuilder() {
    }

    public static QueryBuilder newInstance() {
        return new QueryBuilder();
    }

    public QueryBuilder skip(Integer skip) {
        this.skip = skip;
        return this;
    }

    public QueryBuilder limit(Integer limit) {
        this.limit = limit;
        return this;
    }

    public QueryBuilder order(String order) {
        this.order = order;
        return this;
    }

    public QueryBuilder columns(String... keys) {
        StringBuilder builder = new StringBuilder();
        builder.append(keys[0]);
        for (int i = 1; i < keys.length; i++) {
            builder.append(",").append(keys[i]);
        }
        this.keys = builder.toString();
        return this;
    }

    public QueryBuilder groupBy(String... groupby) {
        if (this.groupby != null) {
            throw new IllegalAccessError("method groupBy() has been called.");
        }
        if (groupby == null || groupby.length == 0) {
            return this;
        }
        StringBuilder builder = new StringBuilder();
        builder.append(groupby[0]);
        for (int i = 1; i < groupby.length; i++) {
            builder.append(",").append(groupby[i]);
        }
        this.groupby = builder.toString();
        return this;
    }

    public QueryBuilder groupCount(Boolean groupcount) {
        this.groupcount = groupcount;
        return this;
    }

    public QueryBuilder count(boolean count) {
        if (count) {
            this.count = 1;
        }
        return this;
    }

    public QueryBuilder where(String key, String value) {
        if (where == null) {
            where = new JsonObject();
        }
        if (this.where.has(key)) {
            throw new IllegalAccessError("key(" + key + ") already exists");
        }
        where.addProperty(key, value);
        return this;
    }

    public QueryBuilder where(String key, Number value) {
        if (where == null) {
            where = new JsonObject();
        }
        if (this.where.has(key)) {
            throw new IllegalAccessError("key(" + key + ") already exists");
        }
        where.addProperty(key, value);
        return this;
    }

    public QueryBuilder where(String key, JsonElement value) {
        if (where == null) {
            where = new JsonObject();
        }
        if (this.where.has(key)) {
            throw new IllegalAccessError("key(" + key + ") already exists");
        }
        where.add(key, value);
        return this;
    }

    public QueryBuilder compare(String key, String operator, Number value) {
        JsonObject json = new JsonObject();
        json.addProperty(operator, value);
        return this.where(key, json);
    }

    /**
     * 大于等于
     * where={"score":{"$gte":1000}}
     */
    public QueryBuilder greaterThanOrEqualTo(String key, Number value) {
        return this.compare(key, "$gte", value);
    }

    /**
     * 小于等于
     * where={"score":{"$lte":1000}}
     */
    public QueryBuilder lessThanOrEqualTo(String key, Number value) {
        return this.compare(key, "$lte", value);
    }

    /**
     * where={"score":{"$in":[1,3,5,7,9]}}
     */
    public QueryBuilder containsIn(String key, String... values) {
        JsonArray array = toJsonArray(values);
        if (array.size() == 0) {
            return this;
        }
        JsonObject inJson = new JsonObject();
        inJson.add("$in", array);
        return this.where(key, inJson);
    }

    public QueryBuilder notContainsIn(String key, String... values) {
        JsonArray array = toJsonArray(values);
        if (array.size() == 0) {
            return this;
        }
        JsonObject ninJson = new JsonObject();
        ninJson.add("$nin", array);
        return this.where(key, ninJson);
    }

    public QueryBuilder exists(String key, boolean exists) {
        JsonObject existsJson = new JsonObject();
        existsJson.addProperty("$exists", exists);
        return this.where(key, existsJson);
    }


    public String build() {
        StringBuilder builder = new StringBuilder();
        if (where != null) {
            builder.append("where=" + where.toString());
        }
        if (skip != null) {
            builder.append("&skip=" + skip);
        }
        if (limit != null) {
            builder.append("&limit=" + limit);
        }
        if (order != null) {
            builder.append("&order=" + order);
        }
        if (groupby != null) {
            builder.append("&groupby=" + groupby);
        }
        if (count != null) {
            builder.append("&count=" + count);
        }
        if (groupcount != null) {
            builder.append("&groupcount=" + groupcount);
        }
        if (builder.length() > 0) {
            if (builder.charAt(0) == '&') {
                builder.deleteCharAt(0);
            }
        }
        return builder.toString();
    }

    private JsonArray toJsonArray(String[] values) {
        JsonArray array = new JsonArray();
        if (values != null) {
            for (String value : values) {
                if (!TextUtils.isEmpty(value)) {
                    array.add(value);
                }
            }
        }
        return array;
    }


}
