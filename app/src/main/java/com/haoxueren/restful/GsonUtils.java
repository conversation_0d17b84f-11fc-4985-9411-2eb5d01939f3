package com.haoxueren.restful;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

public class GsonUtils {

    private static final Gson gson = new Gson();

    public static Gson getGson() {
        return gson;
    }

    public static JsonObject toJsonObject(String json) {
        return gson.fromJson(json, JsonObject.class);
    }

    public static JsonArray toJsonArray(String array) {
        return gson.fromJson(array, JsonArray.class);
    }


}
