package com.haoxueren.base;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.haoxueren.utils.SafetyUtils;

public abstract class BaseActivity extends FragmentActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SafetyUtils.checkAuthCode(this, passed -> {
            if (passed) {
                setContentView(getLayoutResId());
                bindView(getWindow().getDecorView());
                initView();
                initData();
            } else {
                finish();
            }
        });
    }

    public abstract int getLayoutResId();

    protected abstract void bindView(View layout);

    protected abstract void initView();

    protected abstract void initData();

}
