package com.haoxueren.base;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public abstract class BaseFrameLayout extends FrameLayout {

    public BaseFrameLayout(@NonNull Context context) {
        this(context, null);
    }

    public BaseFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        View.inflate(context, getLayoutId(), this);
        this.initView(this);
        this.initData();
    }

    protected abstract int getLayoutId();

    protected abstract void initView(View layout);

    protected abstract void initData();


}
