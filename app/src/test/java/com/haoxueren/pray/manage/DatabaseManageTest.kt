package com.haoxueren.pray.manage

import org.junit.Test
import org.junit.Assert.*

/**
 * 数据库管理功能测试
 */
class DatabaseManageTest {

    @Test
    fun testDatabaseTableCreation() {
        val table = DatabaseTable("TestTable", 100)
        assertEquals("TestTable", table.tableName)
        assertEquals(100, table.recordCount)
        assertFalse(table.isSelected)
    }

    @Test
    fun testDatabaseRecordCreation() {
        val rawData = mapOf(
            "id" to "test123",
            "content" to "test content"
        )
        
        val record = DatabaseRecord(
            primaryField = "Test Primary",
            secondaryField = "Test Secondary",
            timestamp = "2024-01-01 10:00:00",
            rawData = rawData
        )
        
        assertEquals("Test Primary", record.primaryField)
        assertEquals(rawData, record.rawData)
    }

    @Test
    fun testTableConstants() {
        assertEquals("HaoPray", DatabaseTable.TABLE_HAO_PRAY)
        assertEquals("HaoGroup", DatabaseTable.TABLE_HAO_GROUP)
    }
}
