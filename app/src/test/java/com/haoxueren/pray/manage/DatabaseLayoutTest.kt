package com.haoxueren.pray.manage

import org.junit.Test
import org.junit.Assert.*

/**
 * 数据库管理界面布局测试
 */
class DatabaseLayoutTest {

    @Test
    fun testSpinnerDisplayText() {
        // 测试Spinner在选中状态下只显示表名
        val table1 = DatabaseTable("HaoPray", 123)
        val table2 = DatabaseTable("HaoGroup", 45)
        
        assertEquals("HaoPray", table1.tableName)
        assertEquals(123, table1.recordCount)
        assertEquals("HaoGroup", table2.tableName)
        assertEquals(45, table2.recordCount)
    }

    @Test
    fun testTableNameLength() {
        // 测试表名长度适合120dp宽度显示
        val table1 = DatabaseTable("HaoPray", 100)
        val table2 = DatabaseTable("HaoGroup", 50)
        
        // 表名应该不超过10个字符，适合在120dp宽度下显示
        assertTrue("表名长度应该适中", table1.tableName.length <= 10)
        assertTrue("表名长度应该适中", table2.tableName.length <= 10)
    }

    @Test
    fun testTableSelection() {
        // 测试表选择功能的数据结构
        val tables = listOf(
            DatabaseTable("HaoPray", 100),
            DatabaseTable("HaoGroup", 50)
        )
        
        assertEquals(2, tables.size)
        assertEquals("HaoPray", tables[0].tableName)
        assertEquals("HaoGroup", tables[1].tableName)
    }
}
