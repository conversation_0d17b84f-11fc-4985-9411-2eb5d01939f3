package com.haoxueren.pray.group;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 分组数据验证器测试类
 */
public class GroupDataValidatorTest {

    @Test
    public void testValidateGroupId_Valid() {
        // 测试有效的GroupId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validateGroupId("123");
        assertTrue("有效的GroupId应该通过验证", result.isValid);
        assertNull("有效的GroupId不应该有错误消息", result.errorMessage);
    }

    @Test
    public void testValidateGroupId_Empty() {
        // 测试空的GroupId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validateGroupId("");
        assertFalse("空的GroupId应该验证失败", result.isValid);
        assertEquals("错误消息应该正确", "GroupId不能为空", result.errorMessage);
    }

    @Test
    public void testValidateGroupId_OutOfRange() {
        // 测试超出范围的GroupId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validateGroupId("10000");
        assertFalse("超出范围的GroupId应该验证失败", result.isValid);
        assertTrue("错误消息应该包含范围信息", result.errorMessage.contains("范围"));
    }

    @Test
    public void testValidateGroupId_InvalidFormat() {
        // 测试无效格式的GroupId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validateGroupId("abc");
        assertFalse("无效格式的GroupId应该验证失败", result.isValid);
        assertTrue("错误消息应该包含整数信息", result.errorMessage.contains("整数"));
    }

    @Test
    public void testValidatePrayId_Valid() {
        // 测试有效的12位数字PrayId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId("202211281753");
        assertTrue("有效的12位数字PrayId应该通过验证", result.isValid);
        assertNull("有效的PrayId不应该有错误消息", result.errorMessage);
    }

    @Test
    public void testValidatePrayId_Empty() {
        // 测试空的PrayId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId("");
        assertFalse("空的PrayId应该验证失败", result.isValid);
        assertEquals("错误消息应该正确", "PrayId不能为空", result.errorMessage);
    }

    @Test
    public void testValidatePrayId_TooShort() {
        // 测试过短的PrayId（少于12位）
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId("20221128175");
        assertFalse("少于12位的PrayId应该验证失败", result.isValid);
        assertEquals("错误消息应该正确", "PrayId必须是12位数字", result.errorMessage);
    }

    @Test
    public void testValidatePrayId_TooLong() {
        // 测试过长的PrayId（超过12位）
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId("2022112817531");
        assertFalse("超过12位的PrayId应该验证失败", result.isValid);
        assertEquals("错误消息应该正确", "PrayId必须是12位数字", result.errorMessage);
    }

    @Test
    public void testValidatePrayId_InvalidCharacters() {
        // 测试包含非数字字符的PrayId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId("20221128175a");
        assertFalse("包含非数字字符的PrayId应该验证失败", result.isValid);
        assertEquals("错误消息应该正确", "PrayId必须是12位数字", result.errorMessage);
    }

    @Test
    public void testValidatePrayId_WithLetters() {
        // 测试包含字母的PrayId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId("abcd12345678");
        assertFalse("包含字母的PrayId应该验证失败", result.isValid);
        assertEquals("错误消息应该正确", "PrayId必须是12位数字", result.errorMessage);
    }

    @Test
    public void testValidatePrayId_WithSpecialChars() {
        // 测试包含特殊字符的PrayId
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId("202211-28175");
        assertFalse("包含特殊字符的PrayId应该验证失败", result.isValid);
        assertEquals("错误消息应该正确", "PrayId必须是12位数字", result.errorMessage);
    }

    @Test
    public void testValidatePrayContent_Valid() {
        // 测试有效的祈祷内容
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayContent("这是一个有效的祈祷内容");
        assertTrue("有效的祈祷内容应该通过验证", result.isValid);
        assertNull("有效的祈祷内容不应该有错误消息", result.errorMessage);
    }

    @Test
    public void testValidatePrayContent_Empty() {
        // 测试空的祈祷内容（应该允许）
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayContent("");
        assertTrue("空的祈祷内容应该被允许", result.isValid);
        assertNull("空的祈祷内容不应该有错误消息", result.errorMessage);
    }

    @Test
    public void testValidatePrayContent_TooLong() {
        // 测试过长的祈祷内容
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1001; i++) {
            sb.append("a");
        }
        String longContent = sb.toString();
        GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayContent(longContent);
        assertFalse("过长的祈祷内容应该验证失败", result.isValid);
        assertTrue("错误消息应该包含长度信息", result.errorMessage.contains("长度"));
    }

    @Test
    public void testValidateHaoPrayGroup_AllValid() {
        // 测试所有字段都有效的情况
        GroupDataValidator.ValidationResult result = GroupDataValidator.validateHaoPrayGroup("123", "202211281753", "有效的祈祷内容");
        assertTrue("所有字段都有效时应该通过验证", result.isValid);
        assertNull("所有字段都有效时不应该有错误消息", result.errorMessage);
    }

    @Test
    public void testValidateHaoPrayGroup_InvalidGroupId() {
        // 测试GroupId无效的情况
        GroupDataValidator.ValidationResult result = GroupDataValidator.validateHaoPrayGroup("abc", "202211281753", "有效的祈祷内容");
        assertFalse("GroupId无效时应该验证失败", result.isValid);
        assertTrue("错误消息应该与GroupId相关", result.errorMessage.contains("GroupId") || result.errorMessage.contains("整数"));
    }

    @Test
    public void testValidateHaoPrayGroup_InvalidPrayId() {
        // 测试PrayId无效的情况
        GroupDataValidator.ValidationResult result = GroupDataValidator.validateHaoPrayGroup("123", "invalid", "有效的祈祷内容");
        assertFalse("PrayId无效时应该验证失败", result.isValid);
        assertTrue("错误消息应该与PrayId相关", result.errorMessage.contains("PrayId") || result.errorMessage.contains("12位数字"));
    }



    @Test
    public void testIsValidGroupIdRange() {
        // 测试GroupId范围检查
        assertTrue("0应该在有效范围内", GroupDataValidator.isValidGroupIdRange(0));
        assertTrue("999应该在有效范围内", GroupDataValidator.isValidGroupIdRange(999));
        assertTrue("-999应该在有效范围内", GroupDataValidator.isValidGroupIdRange(-999));
        assertFalse("10000应该超出有效范围", GroupDataValidator.isValidGroupIdRange(10000));
        assertFalse("-1000应该超出有效范围", GroupDataValidator.isValidGroupIdRange(-1000));
    }

    @Test
    public void testIsSystemReservedGroupId() {
        // 测试系统保留GroupId检查
        assertTrue("负数应该是系统保留的", GroupDataValidator.isSystemReservedGroupId(-1));
        assertFalse("正数不应该是系统保留的", GroupDataValidator.isSystemReservedGroupId(1));
        assertFalse("0不应该是系统保留的", GroupDataValidator.isSystemReservedGroupId(0));
    }

    @Test
    public void testValidatePrayId_EdgeCases() {
        // 测试边界情况

        // 全零的12位数字
        GroupDataValidator.ValidationResult result1 = GroupDataValidator.validatePrayId("000000000000");
        assertTrue("全零的12位数字应该有效", result1.isValid);

        // 全9的12位数字
        GroupDataValidator.ValidationResult result2 = GroupDataValidator.validatePrayId("999999999999");
        assertTrue("全9的12位数字应该有效", result2.isValid);

        // 混合数字
        GroupDataValidator.ValidationResult result3 = GroupDataValidator.validatePrayId("123456789012");
        assertTrue("混合数字的12位应该有效", result3.isValid);

        // 11位数字
        GroupDataValidator.ValidationResult result4 = GroupDataValidator.validatePrayId("12345678901");
        assertFalse("11位数字应该无效", result4.isValid);

        // 13位数字
        GroupDataValidator.ValidationResult result5 = GroupDataValidator.validatePrayId("1234567890123");
        assertFalse("13位数字应该无效", result5.isValid);
    }

    @Test
    public void testValidatePrayId_MultipleValidFormats() {
        // 测试多种有效的12位数字格式
        String[] validPrayIds = {
            "202211281753",  // 日期时间格式
            "123456789012",  // 连续数字
            "000000000001",  // 前导零
            "999999999999",  // 最大值
            "100000000000",  // 1后面11个0
            "010203040506"   // 交替数字
        };

        for (String prayId : validPrayIds) {
            GroupDataValidator.ValidationResult result = GroupDataValidator.validatePrayId(prayId);
            assertTrue("PrayId '" + prayId + "' 应该有效", result.isValid);
            assertNull("有效的PrayId不应该有错误消息", result.errorMessage);
        }
    }
}
