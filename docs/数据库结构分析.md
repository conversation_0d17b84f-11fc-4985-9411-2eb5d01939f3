# HaoPray Android项目数据库结构分析

## 概述

本文档详细分析了HaoPray Android项目的数据库结构，包括使用的技术栈、表结构定义、数据模型以及相关的操作方法。

## 1. 数据库技术栈

### 1.1 核心技术
- **数据库类型**: SQLite
- **数据库文件**: `HaoPray.db`
- **操作方式**: 原生Android SQLiteDatabase API
- **帮助类**: 自定义`SQLiteHelper`类

### 1.2 相关工具和库
- **SQLiteStudioRemote**: 用于远程数据库管理和调试
  - 端口: 2010
  - 密码: haoxueren
- **CursorProxy**: 自定义游标代理类，简化数据库查询操作
- **ContentValues**: 用于数据插入和更新操作

## 2. 数据库表结构

### 2.1 HaoGroup表

**用途**: 存储祷告分组信息

```sql
CREATE TABLE HaoGroup (
    objectId  VARCHAR UNIQUE PRIMARY KEY,
    groupId   INTEGER,
    prayId    VARCHAR,
    pray      TEXT,
    createdAt DATE,
    updatedAt DATE
);
```

**字段详细说明**:

| 字段名 | 数据类型 | 约束条件 | 说明 |
|--------|----------|----------|------|
| objectId | VARCHAR | UNIQUE, PRIMARY KEY | 唯一标识符，主键 |
| groupId | INTEGER | - | 分组ID，用于分组管理 |
| prayId | VARCHAR | - | 关联的祷告ID |
| pray | TEXT | - | 祷告内容（Base16编码存储） |
| createdAt | DATE | - | 记录创建时间 |
| updatedAt | DATE | - | 记录更新时间 |

### 2.2 HaoPray表

**用途**: 存储祷告记录信息

```sql
CREATE TABLE HaoPray (
    objectId  VARCHAR UNIQUE PRIMARY KEY,
    id        VARCHAR,
    date      VARCHAR,
    count     INTEGER,
    pray      TEXT,
    updatedAt DATE,
    createdAt DATE
);
```

**字段详细说明**:

| 字段名 | 数据类型 | 约束条件 | 说明 |
|--------|----------|----------|------|
| objectId | VARCHAR | UNIQUE, PRIMARY KEY | 唯一标识符，主键 |
| id | VARCHAR | - | 祷告ID，用于标识不同的祷告类型 |
| date | VARCHAR | - | 祷告日期 |
| count | INTEGER | - | 祷告次数或计数 |
| pray | TEXT | - | 祷告内容（Base16编码存储） |
| updatedAt | DATE | - | 记录更新时间 |
| createdAt | DATE | - | 记录创建时间 |

## 3. 表关系和索引

### 3.1 表关系
- **HaoGroup** 和 **HaoPray** 表通过 `prayId` 和 `id` 字段建立逻辑关联
- 没有定义外键约束，关系通过应用层逻辑维护

### 3.2 索引
- 每个表的主键 `objectId` 字段自动创建唯一索引
- 没有额外定义的复合索引或普通索引

## 4. 数据库操作方法

### 4.1 HaoPray表操作

**插入操作**:
```java
public Observable<String> insertHaoPray(HaoPray haoPray)
```

**查询操作**:
```java
public List<HaoPray> queryHaoPray(String id, int skip, int limit)
public Observable<Map<String, Integer>> queryIdCount(String id)
public List<HaoPray> queryHaoPrayByDate(String date)
public List<HaoPray> queryHaoPrayByDateRange(String startDate, String endDate)
```

**更新操作**:
```java
public Observable<Boolean> updateHaoPray(HaoPray haoPray)
public Observable<Boolean> updateHaoPrayCount(String objectId, int count)
```

**删除操作**:
```java
public Observable<Boolean> deleteHaoPray(String objectId)
public Observable<Boolean> deleteHaoPrayById(String id)
```

### 4.2 HaoGroup表操作

**插入操作**:
```java
public Observable<String> insertHaoGroup(HaoGroup haoGroup)
```

**查询操作**:
```java
public List<HaoGroup> queryHaoGroup(int skip, int limit)
public List<HaoGroup> queryHaoGroupByGroupId(int groupId)
public HaoGroup queryHaoGroupByPrayId(String prayId)
```

**更新操作**:
```java
public Observable<Boolean> updateHaoGroup(HaoGroup haoGroup)
public Observable<Boolean> updateHaoGroupPray(String objectId, String pray)
```

**删除操作**:
```java
public Observable<Boolean> deleteHaoGroup(String objectId)
public Observable<Boolean> deleteHaoGroupByGroupId(int groupId)
```

## 5. 数据编码和解码

### 5.1 Base16编码
- **用途**: 祷告内容在存储时使用Base16编码
- **原因**: 避免特殊字符导致的数据库操作问题
- **实现**: 使用自定义的编码解码方法

### 5.2 编码方法
```java
public static String encodeBase16(String text) {
    if (text == null || text.isEmpty()) {
        return "";
    }
    return bytesToHex(text.getBytes(StandardCharsets.UTF_8));
}
```

### 5.3 解码方法
```java
public static String decodeBase16(String hex) {
    if (hex == null || hex.isEmpty()) {
        return "";
    }
    try {
        byte[] bytes = hexToBytes(hex);
        return new String(bytes, StandardCharsets.UTF_8);
    } catch (Exception e) {
        return hex; // 如果解码失败，返回原始字符串
    }
}
```

## 6. 性能优化

### 6.1 查询优化
- 使用分页查询避免大数据量加载
- 合理使用索引提高查询效率
- 避免在主线程执行数据库操作

### 6.2 内存管理
- 及时关闭Cursor对象
- 使用连接池管理数据库连接
- 避免内存泄漏

## 7. 数据同步

### 7.1 本地同步
- 支持多设备间的数据库同步
- 使用Socket通信进行数据传输
- 实现增量同步和全量同步

### 7.2 冲突处理
- 基于时间戳的冲突解决策略
- 支持手动冲突解决
- 保证数据一致性

## 8. 安全性

### 8.1 数据保护
- 敏感数据使用加密存储
- 访问控制和权限管理
- 防止SQL注入攻击

### 8.2 备份恢复
- 定期数据备份
- 支持数据导入导出
- 灾难恢复机制

## 总结

HaoPray项目的数据库设计简洁高效，采用SQLite作为本地存储方案，通过合理的表结构设计和优化的操作方法，为应用提供了稳定可靠的数据存储服务。Base16编码确保了数据的安全存储，而完善的同步机制则支持了多设备间的数据共享。
