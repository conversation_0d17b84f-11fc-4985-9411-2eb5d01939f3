# 分组编辑功能使用指南

## 功能概述

本功能为HaoPray Android应用的「所有分组」页面实现了完整的长按编辑功能，允许用户修改分组条目的详细信息，并自动处理数据同步。

## 主要特性

### 1. 长按编辑触发
- **触发方式**：在「所有分组」页面长按任意分组条目
- **支持区域**：分组ID区域、祈祷内容区域、整个条目区域
- **响应时间**：长按约500ms后触发编辑对话框

### 2. 可编辑字段
- **分组ID (GroupId)**：整数类型，范围-999到9999
- **祈祷ID (PrayId)**：字符串类型，3-50个字符，只能包含字母、数字、下划线和连字符
- **祈祷内容 (Pray)**：文本类型，最多1000个字符，可为空

### 3. 数据验证
- **实时验证**：输入时进行格式和范围检查
- **错误提示**：清晰的错误消息指导用户正确输入
- **冲突检查**：防止PrayId重复使用

### 4. 数据同步
- **自动同步**：当PrayId发生变化时，自动更新HaoPray表中对应记录的id字段
- **事务保护**：使用数据库事务确保数据一致性
- **回滚机制**：出错时自动回滚，保持数据完整性

## 使用步骤

### 编辑分组信息

1. **打开分组页面**
   - 在主界面点击菜单中的「分组」选项
   - 进入「所有分组」页面

2. **触发编辑**
   - 长按要编辑的分组条目
   - 等待编辑对话框弹出

3. **修改信息**
   - 在「分组ID」字段输入新的分组ID（必填）
   - 在「祈祷ID」字段输入新的祈祷ID（必填）
   - 在「祈祷内容」字段输入新的祈祷内容（可选）

4. **保存更改**
   - 点击「更新」按钮保存修改
   - 系统会自动验证数据并处理同步
   - 成功后显示「更新成功」提示

### 数据验证规则

#### 分组ID (GroupId)
- **类型**：整数
- **范围**：-999 到 9999
- **示例**：`123`, `-50`, `0`

#### 祈祷ID (PrayId)
- **类型**：字符串
- **长度**：3-50个字符
- **格式**：只能包含字母、数字、下划线(_)和连字符(-)
- **示例**：`morning_pray`, `evening-prayer`, `pray123`

#### 祈祷内容 (Pray)
- **类型**：文本
- **长度**：最多1000个字符
- **格式**：无特殊限制
- **可选**：可以为空

## 技术实现

### 核心组件

1. **UpdateGroupDialog**：编辑对话框，处理用户输入和数据验证
2. **GroupDataValidator**：数据验证工具类，确保输入数据的有效性
3. **SQLiteHelper**：数据库操作类，实现数据同步逻辑
4. **AllGroupActivity**：分组列表页面，处理长按事件和UI更新

### 数据同步机制

当用户修改PrayId时，系统会：

1. **验证新PrayId**：检查格式和唯一性
2. **开始事务**：确保操作的原子性
3. **更新HaoGroup表**：保存新的分组信息
4. **同步HaoPray表**：将旧PrayId对应的记录更新为新PrayId
5. **提交事务**：确认所有更改
6. **更新UI**：刷新界面显示

### 错误处理

- **数据验证失败**：显示具体的错误信息
- **PrayId冲突**：提示该ID已被其他分组使用
- **数据库错误**：自动回滚并显示错误信息
- **网络错误**：在API模式下处理网络异常

## 注意事项

### 使用建议

1. **备份数据**：重要数据修改前建议备份
2. **谨慎修改PrayId**：PrayId变更会影响关联的祈祷记录
3. **检查依赖**：确认没有其他功能依赖当前的PrayId

### 限制说明

1. **系统保留ID**：负数GroupId通常为系统保留，修改时需注意
2. **API模式限制**：在API模式下，数据同步依赖服务端支持
3. **并发操作**：避免同时编辑同一分组

## 测试验证

### 单元测试
- `GroupDataValidatorTest`：验证数据验证逻辑
- 覆盖各种边界条件和异常情况

### 集成测试
- `GroupEditIntegrationTest`：验证完整的编辑和同步流程
- 测试数据一致性和错误处理

### 手动测试建议

1. **正常编辑流程**：测试各字段的正常修改
2. **数据验证**：输入无效数据验证错误提示
3. **PrayId同步**：修改PrayId后检查数据一致性
4. **冲突处理**：尝试使用重复的PrayId
5. **UI响应**：确认界面正确更新

## 故障排除

### 常见问题

1. **编辑对话框不弹出**
   - 检查长按时间是否足够
   - 确认点击区域是否正确

2. **数据验证失败**
   - 检查输入格式是否符合要求
   - 确认字段长度是否在范围内

3. **保存失败**
   - 检查PrayId是否与其他分组冲突
   - 确认数据库连接是否正常

4. **数据不同步**
   - 检查SQLite模式下的事务处理
   - 确认API模式下的服务端支持

### 日志查看

在Android Studio的Logcat中查看相关日志：
- 标签：`SQLiteHelper`
- 级别：Debug/Error
- 关键词：`Synced HaoPray records`、`Error updating group`

## 版本历史

- **v1.0**：初始版本，支持基本的编辑功能
- **v1.1**：添加数据验证和错误处理
- **v1.2**：实现PrayId变更时的数据同步
- **v1.3**：增强UI响应和用户体验
