# 数据库管理功能说明

## 功能概述

HaoPray应用新增了数据库管理功能，允许用户直接在应用内查看和搜索数据库中的数据。

## 功能特性

### 1. 主页菜单入口
- 在应用主页的下拉菜单中添加了"数据库管理"选项
- 点击即可进入数据库管理界面

### 2. 表选择功能
- 左侧边栏显示数据库中的所有表
- 显示表名和记录数量
- 支持点击选择要查看的表
- 当前支持的表：
  - HaoPray：祷告记录表
  - HaoGroup：祷告分组表

### 3. 数据查看和搜索
- 选择表后，右侧主内容区域显示表数据
- 提供搜索输入框，支持模糊查询
- 支持按任意字段内容进行搜索
- 自动解码Base16编码的祷告内容

### 4. 性能优化
- 分页加载：每页加载20条记录
- 滚动到底部自动加载更多数据
- 下拉刷新支持
- 防重复请求保护

### 5. 用户体验
- 空状态提示
- 加载状态指示
- 错误处理和用户提示
- 输入验证（搜索关键词长度限制）
- 支持回车键搜索

## 使用方法

1. **进入数据库管理**
   - 在主页点击右上角的"更多"按钮（三个点）
   - 选择"数据库管理"

2. **选择表**
   - 在左侧边栏点击要查看的表
   - 表名旁边显示记录数量

3. **查看数据**
   - 选择表后，右侧会显示最新的记录
   - 每条记录显示主要信息、次要信息和时间信息

4. **搜索数据**
   - 在搜索框中输入关键词
   - 点击"搜索"按钮或按回车键
   - 支持模糊匹配任意字段内容

5. **加载更多**
   - 向下滚动到底部自动加载更多数据
   - 下拉刷新重新加载数据

## 技术实现

### 新增文件
- `DatabaseManageActivity.kt` - 主要的数据库管理界面
- `DatabaseTable.kt` - 表信息数据模型
- `DatabaseRecord.kt` - 查询结果数据模型
- `DatabaseTableAdapter.kt` - 表列表适配器
- `DatabaseRecordAdapter.kt` - 查询结果适配器
- 相关布局文件和测试文件

### 修改文件
- `MainToolbarCompose.kt` - 添加数据库管理菜单项
- `MainActivity.kt` - 添加菜单点击处理
- `SQLiteHelper.java` - 添加数据库查询方法
- `AndroidManifest.xml` - 注册新Activity

### 核心方法
- `getTableInfoList()` - 获取表信息
- `searchInTable()` - 表内搜索
- `getLatestRecords()` - 获取最新记录
- 支持分页的查询方法

## 注意事项

1. 搜索关键词长度限制为20个字符
2. 分页加载每页20条记录，避免大数据量卡顿
3. 祷告内容会自动解码Base16编码
4. 所有数据库操作在后台线程执行，避免阻塞UI
5. 使用CompositeDisposable管理RxJava订阅，防止内存泄漏

## 未来扩展

- 支持数据编辑功能
- 支持数据导出
- 支持更多表的管理
- 支持高级搜索过滤器
- 支持数据统计图表
