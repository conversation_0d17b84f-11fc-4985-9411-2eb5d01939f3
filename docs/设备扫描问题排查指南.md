# 设备扫描问题排查指南

## 问题描述
两台设备都安装了HaoPray应用，但点击「扫描设备」却找不到另一台设备。

## 可能的原因

### 1. 网络连接问题
- **WiFi未连接**：确保两台设备都连接到同一个WiFi网络
- **网络隔离**：路由器启用了AP隔离功能，阻止设备间通信
- **IP地址获取失败**：设备无法获取有效的IP地址

### 2. 服务器未启动
- **Socket服务器未运行**：应用的Socket服务器可能未正确启动
- **端口被占用**：2024端口可能被其他应用占用
- **UDP响应服务器未启动**：设备发现的UDP服务器未运行

### 3. 防火墙/权限问题
- **Android网络权限**：应用可能缺少必要的网络权限
- **系统防火墙**：Android系统可能阻止了网络扫描

### 4. 扫描机制问题
- **扫描超时**：网络延迟导致扫描超时
- **扫描范围**：只扫描了部分IP地址范围

## 解决方案

### 1. 检查网络连接
```
1. 确保两台设备连接到同一WiFi网络
2. 检查WiFi信号强度
3. 重启路由器（如果可能）
4. 检查路由器是否启用了AP隔离功能
```

### 2. 使用网络诊断功能
```
1. 打开数据库同步页面
2. 点击右上角菜单 → "网络诊断"
3. 查看诊断报告，确认：
   - 网络已连接
   - 获取到有效IP地址
   - 端口2024和2025可用
```

### 3. 使用设备扫描测试
```
1. 打开数据库同步页面
2. 点击右上角菜单 → "测试功能" → "设备扫描测试"
3. 依次运行：
   - 常见问题检查
   - 网络连通性测试
   - UDP广播测试
   - 完整扫描测试
4. 查看Logcat日志了解详细信息
```

### 4. 手动检查服务器状态
```
1. 确保两台设备都启动了应用
2. 进入数据库同步页面（这会自动启动服务器）
3. 检查日志确认服务器已启动
```

## 改进的扫描机制

### 双重发现机制
应用现在使用两种方式发现设备：

1. **UDP广播发现**（快速）
   - 发送UDP广播消息到局域网
   - 其他设备响应广播
   - 适用于支持广播的网络环境

2. **TCP端口扫描**（全面）
   - 扫描局域网内所有IP的2024端口
   - 发送设备信息请求
   - 适用于所有网络环境

### 增强的错误处理
- 更详细的扫描进度提示
- 显示本机IP地址
- 区分UDP和TCP发现的设备
- 避免重复添加相同设备

## 调试步骤

### 1. 查看应用日志
```bash
adb logcat | grep -E "(DeviceScanTest|DeviceDiscovery|EnhancedSocket)"
```

### 2. 检查网络状态
```bash
adb shell ip addr show wlan0
adb shell netstat -an | grep 2024
```

### 3. 测试网络连通性
```bash
# 在设备A上获取IP地址，然后在设备B上测试
adb shell ping -c 3 <设备A的IP地址>
```

## 常见问题解答

### Q: 为什么有时能发现设备，有时不能？
A: 这通常是网络延迟或临时网络问题导致的。新的扫描机制增加了重试和超时处理。

### Q: 扫描很慢怎么办？
A: 新版本优先使用UDP广播发现，速度更快。TCP扫描作为备用方案。

### Q: 两台设备在不同网段怎么办？
A: 目前只支持同一局域网内的设备发现。确保设备连接到同一WiFi网络。

### Q: 企业网络环境下无法发现设备？
A: 企业网络可能有严格的防火墙规则。联系网络管理员开放2024和2025端口。

## 技术细节

### 端口使用
- **2024**: TCP Socket服务器端口（数据同步）
- **2025**: UDP广播发现端口（设备发现）

### 扫描流程
1. 获取本机IP地址
2. 启动UDP响应服务器
3. 发送UDP广播消息
4. 监听UDP响应（3秒）
5. 扫描TCP端口（网络段内所有IP）
6. 合并去重发现的设备

### 超时设置
- UDP广播超时：3秒
- TCP连接超时：2秒
- 总扫描超时：约30秒

## 联系支持
如果按照以上步骤仍无法解决问题，请提供：
1. 网络诊断报告
2. 设备扫描测试日志
3. 两台设备的型号和Android版本
4. 路由器型号和网络环境描述
