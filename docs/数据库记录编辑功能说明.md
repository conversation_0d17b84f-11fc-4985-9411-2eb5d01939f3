# 数据库记录编辑功能说明

## 功能概述

为HaoPray Android应用的数据库管理界面新增了记录编辑功能，用户可以通过点击数据库记录来编辑其内容。

## 功能特性

### 1. 点击编辑
- 在数据库管理界面中，用户可以点击任意一条记录
- 点击后会弹出编辑对话框，显示该记录的所有字段信息

### 2. 编辑对话框
- **动态字段生成**：根据记录的实际字段动态生成输入框
- **字段标签**：每个字段都有清晰的中文标签显示
- **只读字段**：objectId、createdAt、updatedAt等字段设为只读，不可编辑
- **输入类型优化**：
  - 数字字段（count、groupId）使用数字键盘
  - 祷告内容字段支持多行输入
  - 日期字段提供格式提示

### 3. 数据验证
- **必填字段验证**：
  - HaoPray表：id、date、pray为必填
  - HaoGroup表：groupId、prayId为必填
- **数据类型验证**：
  - 数字字段验证输入是否为有效数字
  - 日期字段验证格式是否为yyyy-MM-dd
- **实时错误提示**：验证失败时显示具体错误信息

### 4. 数据处理
- **Base16编码处理**：祷告内容自动进行Base16编码/解码
- **字段类型转换**：自动处理字符串、数字等类型转换
- **时间戳更新**：保存时自动更新updatedAt字段

### 5. 用户体验
- **保存状态指示**：保存时按钮显示"保存中..."状态
- **成功反馈**：保存成功后显示提示并自动关闭对话框
- **错误处理**：保存失败时显示具体错误信息
- **数据刷新**：保存成功后自动刷新列表显示最新数据

## 技术实现

### 新增文件

1. **DatabaseRecordEditDialog.kt**
   - 编辑对话框的主要实现类
   - 处理字段动态生成、数据验证、保存操作

2. **dialog_database_record_edit.xml**
   - 编辑对话框的主布局文件
   - 包含标题、字段容器、错误提示、操作按钮

3. **item_edit_field.xml**
   - 单个字段编辑项的布局文件
   - 包含字段标签和输入框

4. **shape_button_outline.xml / shape_button_primary.xml**
   - 按钮背景样式文件

5. **DatabaseRecordEditTest.kt**
   - 编辑功能的单元测试

### 修改文件

1. **SQLiteHelper.java**
   - 新增 `updateHaoPray(HaoPray)` 方法
   - 新增 `updateHaoPrayFields(String, Map)` 方法
   - 新增 `updateHaoGroupFields(String, Map)` 方法

2. **DatabaseRecordAdapter.kt**
   - 添加点击监听器参数
   - 在ViewHolder中设置点击事件

3. **DatabaseManageActivity.kt**
   - 集成编辑对话框
   - 添加记录点击处理方法
   - 添加数据刷新方法

## 使用方法

1. **进入数据库管理**
   - 在主页点击"更多" → "数据库管理"

2. **选择表和记录**
   - 在左侧选择要查看的表（HaoPray或HaoGroup）
   - 在右侧查看记录列表

3. **编辑记录**
   - 点击任意一条记录
   - 在弹出的对话框中编辑字段内容
   - 点击"保存"提交更改，或点击"取消"放弃更改

4. **字段说明**
   - **只读字段**：对象ID、创建时间、更新时间（灰色显示）
   - **必填字段**：根据表类型不同，某些字段为必填
   - **特殊字段**：祷告内容支持多行输入，数字字段使用数字键盘

## 数据安全

- **事务处理**：数据库更新操作使用事务确保数据一致性
- **输入验证**：严格的客户端验证防止无效数据
- **错误恢复**：操作失败时提供明确的错误信息
- **备份建议**：重要数据修改前建议先备份

## 注意事项

1. **祷告内容编码**：祷告内容在数据库中以Base16编码存储，编辑时会自动解码显示
2. **日期格式**：日期字段必须使用yyyy-MM-dd格式
3. **数字字段**：count和groupId字段只接受整数输入
4. **网络操作**：所有数据库操作在后台线程执行，不会阻塞UI
5. **内存管理**：使用CompositeDisposable管理RxJava订阅，防止内存泄漏

## 扩展建议

- 支持批量编辑功能
- 添加字段历史记录查看
- 支持更多数据类型的验证
- 添加字段级别的权限控制
- 支持自定义验证规则
