# Android 构建产物
*.apk
*.aab
*.dex
*.class
/build/
/app/build/
/captures/
.externalNativeBuild/
.cxx/

# Gradle 相关
.gradle/
/local.properties
gradle-wrapper.jar
gradlew
gradlew.bat

# IDE 配置文件
.idea/
*.iml
.vscode/
.settings/
.project
.classpath
.kotlin/sessions/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件和缓存
*.tmp
*.temp
*.cache
.temp/
.cache/

# 第三方库和依赖
/app/libs/*.jar
node_modules/
.pnp/
.pnp.js

# 测试覆盖率报告
/coverage/
*.lcov

# 签名文件（安全考虑）
*.jks
*.keystore
keystore.properties

# ProGuard 生成的文件
/app/proguard/
mapping.txt

# Android Studio 导航编辑器文件
.navigation/

# Android Studio 捕获文件夹
captures/

# IntelliJ
out/

# Keystore 文件
# 如果你不想检查你的密钥库文件到VCS，那么取消注释以下行：
#*.jks
#*.keystore

# 外部原生构建文件夹由Android Studio生成。
# 这些文件夹包含编译的原生库，不应该被跟踪。
.externalNativeBuild/
.cxx/

# Google Services (例如 APIs 或 Firebase)
# google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# Version control
.svn/
.hg/
.bzr/

# Backup files
*.bak
*.backup
*~

# Package files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# 保留重要的配置和源代码文件
# 以下文件类型将被保留用于代码理解：
# - *.java (Java 源代码)
# - *.kt (Kotlin 源代码)
# - *.xml (布局、清单、资源文件)
# - *.gradle (构建脚本)
# - *.properties (配置文件)
# - *.md (文档文件)
# - *.json (配置文件)
# - *.yml, *.yaml (配置文件)