---
description: 
globs: 
alwaysApply: true
---
## 设置界面

这是一个用于配置应用设置的界面，包括以下功能：

- **API 模式切换**：通过 `modeSwitch` 控件切换应用的 API 模式，影响数据请求方式。
- **键盘功能开关**：通过 `keyboardSwitch` 控件控制是否启用自定义键盘功能。
- **指纹验证开关**：通过 `fingerprintSwitch` 控件启用或禁用指纹验证，提升安全性。
- **服务器地址设置**：可在 `serverEditText` 输入远程主机地址，用于数据同步。
- **数据备份**：点击 `backupButton` 可将本地 SQLite 数据库备份到本地文件。
- **数据同步**：点击 `syncButton` 可将本地数据同步到指定服务器。
- **局域网服务器自动扫描**：应用启动时会自动扫描局域网内可用的服务器，并自动填充服务器地址。
- **保持屏幕常亮**：进入设置界面时屏幕保持常亮，离开时恢复默认。
- **Socket 服务管理**：进入界面时自动启动 Socket 服务，离开时关闭。

文件路径: [app/src/main/java/com/haoxueren/pray/manage/SettingsActivity.java](mdc:app/src/main/java/com/haoxueren/pray/manage/SettingsActivity.java)

