---
description: 
globs: 
alwaysApply: true
---
# Jetpack Compose 相关规则

本项目使用 Jetpack Compose 进行部分 UI 构建。

## 主要 Compose 文件和配置：

*   **Compose Activity 示例**: [app/src/main/java/com/haoxueren/pray/manage/StatisticsActivity.kt](mdc:app/src/main/java/com/haoxueren/pray/manage/StatisticsActivity.kt) 是一个使用 Jetpack Compose 构建的 Activity 示例，展示了 Composable 函数的用法。
*   **Compose 相关依赖和配置**: 可以在 [app/build.gradle](mdc:app/build.gradle) 文件中找到 Jetpack Compose 的相关依赖和构建特性配置。

## 一般约定：

*   新的 UI 界面可以考虑使用 Jetpack Compose 实现。
*   Composable 函数应遵循 Compose 的设计模式和最佳实践。
