---
description: 
globs: 
alwaysApply: true
---
# 功能模块指南

本项目包含以下主要功能模块：

## 祷告功能 (主功能)
* **主页**: [app/src/main/java/com/haoxueren/pray/main/MainActivity.java](mdc:app/src/main/java/com/haoxueren/pray/main/MainActivity.java)
* **数据服务**: [app/src/main/java/com/haoxueren/pray/service/DataService.java](mdc:app/src/main/java/com/haoxueren/pray/service/DataService.java)
* **所有分组**: [app/src/main/java/com/haoxueren/pray/group/AllGroupActivity.java](mdc:app/src/main/java/com/haoxueren/pray/group/AllGroupActivity.java)
* **应用配置**: [app/src/main/java/com/haoxueren/pray/config](mdc:app/src/main/java/com/haoxueren/pray/config)

## 核心基础功能
* **SQLite 功能**: [app/src/main/java/com/haoxueren/sqlite](mdc:app/src/main/java/com/haoxueren/sqlite)
* **通用工具**: [app/src/main/java/com/haoxueren/utils](mdc:app/src/main/java/com/haoxueren/utils)
* **基础组件**: [app/src/main/java/com/haoxueren/base](mdc:app/src/main/java/com/haoxueren/base)
* **自定义视图**: [app/src/main/java/com/haoxueren/view](mdc:app/src/main/java/com/haoxueren/view)
* **自定义控件**: [app/src/main/java/com/haoxueren/widget](mdc:app/src/main/java/com/haoxueren/widget)
* **对话框组件**: [app/src/main/java/com/haoxueren/dialog](mdc:app/src/main/java/com/haoxueren/dialog)
* **代理模式实现**: [app/src/main/java/com/haoxueren/proxy](mdc:app/src/main/java/com/haoxueren/proxy)
