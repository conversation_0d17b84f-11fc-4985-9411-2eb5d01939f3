{"name": "测试环境配置", "description": "测试环境配置（接近生产）", "environment": "staging", "logging": {"level": "INFO", "format": "json", "output": "both", "file_rotation": true, "max_file_size": "50MB", "max_files": 10, "log_directory": "/var/log/app"}, "database": {"host": "${STAGING_DB_HOST}", "port": 5432, "database": "${STAGING_DB_NAME}", "username": "${STAGING_DB_USER}", "password": "${STAGING_DB_PASSWORD}", "ssl": true, "ssl_mode": "require", "connection_pool": {"min": 2, "max": 10, "idle_timeout": 30000}}, "redis": {"host": "${STAGING_REDIS_HOST}", "port": 6379, "password": "${STAGING_REDIS_PASSWORD}", "database": 1, "ssl": true}, "api": {"base_url": "${STAGING_API_BASE_URL}", "timeout": 15000, "retries": 2, "rate_limit": {"requests_per_minute": 200}}, "features": {"debug_mode": false, "mock_data": false, "detailed_errors": true, "cors_enabled": true, "hot_reload": false}, "security": {"cors_origins": ["${STAGING_CORS_ORIGINS}"], "jwt_secret": "${STAGING_JWT_SECRET}", "token_expiry": "2h", "password_min_length": 8, "enable_swagger": true, "rate_limiting": true, "captcha": true}, "performance": {"cache_ttl": 1800, "compression": true, "request_logging": true, "query_logging": true, "enable_cdn": false}, "external_services": {"email": {"provider": "sendgrid", "from_address": "<EMAIL>"}, "sms": {"provider": "twi<PERSON>"}, "storage": {"provider": "s3", "bucket": "${STAGING_S3_BUCKET}", "region": "${STAGING_AWS_REGION}"}, "monitoring": {"sentry": {"enabled": true, "dsn": "${STAGING_SENTRY_DSN}"}}}, "health_checks": {"enabled": true, "endpoint": "/health", "checks": ["database", "redis"]}}