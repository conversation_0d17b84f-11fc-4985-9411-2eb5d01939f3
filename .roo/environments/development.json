{"name": "开发环境配置", "description": "开发环境专用配置", "environment": "development", "logging": {"level": "DEBUG", "format": "detailed", "output": "console", "file_rotation": false, "max_file_size": "10MB", "max_files": 5}, "database": {"host": "localhost", "port": 5432, "database": "dev_db", "username": "dev_user", "password": "dev_password", "ssl": false, "connection_pool": {"min": 1, "max": 5, "idle_timeout": 30000}}, "redis": {"host": "localhost", "port": 6379, "password": null, "database": 0, "ssl": false}, "api": {"base_url": "http://localhost:8080", "timeout": 30000, "retries": 1, "rate_limit": {"requests_per_minute": 1000}}, "features": {"debug_mode": true, "mock_data": true, "detailed_errors": true, "cors_enabled": true, "hot_reload": true}, "security": {"cors_origins": ["http://localhost:3000", "http://localhost:8080"], "jwt_secret": "dev-secret-key-change-in-production", "token_expiry": "24h", "password_min_length": 6, "enable_swagger": true}, "performance": {"cache_ttl": 300, "compression": false, "request_logging": true, "query_logging": true}, "external_services": {"email": {"provider": "mock", "from_address": "<EMAIL>"}, "sms": {"provider": "mock"}, "storage": {"provider": "local", "path": "./uploads"}}}