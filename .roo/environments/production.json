{"name": "生产环境配置", "description": "生产环境专用配置（安全优化）", "environment": "production", "logging": {"level": "INFO", "format": "json", "output": "file", "file_rotation": true, "max_file_size": "100MB", "max_files": 30, "log_directory": "/var/log/app"}, "database": {"host": "${DB_HOST}", "port": 5432, "database": "${DB_NAME}", "username": "${DB_USER}", "password": "${DB_PASSWORD}", "ssl": true, "ssl_mode": "require", "connection_pool": {"min": 5, "max": 20, "idle_timeout": 60000}}, "redis": {"host": "${REDIS_HOST}", "port": 6379, "password": "${REDIS_PASSWORD}", "database": 0, "ssl": true}, "api": {"base_url": "${API_BASE_URL}", "timeout": 10000, "retries": 3, "rate_limit": {"requests_per_minute": 100}}, "features": {"debug_mode": false, "mock_data": false, "detailed_errors": false, "cors_enabled": true, "hot_reload": false}, "security": {"cors_origins": ["${CORS_ORIGINS}"], "jwt_secret": "${JWT_SECRET}", "token_expiry": "1h", "password_min_length": 8, "enable_swagger": false, "rate_limiting": true, "captcha": true}, "performance": {"cache_ttl": 3600, "compression": true, "request_logging": true, "query_logging": false, "enable_cdn": true}, "external_services": {"email": {"provider": "sendgrid", "from_address": "<EMAIL>"}, "sms": {"provider": "twi<PERSON>"}, "storage": {"provider": "s3", "bucket": "${S3_BUCKET}", "region": "${AWS_REGION}"}, "monitoring": {"sentry": {"enabled": true, "dsn": "${SENTRY_DSN}"}, "newrelic": {"enabled": true, "app_name": "${APP_NAME}"}}}, "health_checks": {"enabled": true, "endpoint": "/health", "checks": ["database", "redis", "external_services"]}}