# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE文件
.idea/
.vscode/
*.swp
*.swo
*~

# 构建输出
build/
dist/
target/
*.class
*.jar
*.war
*.ear
*.nar

# 依赖
node_modules/
.gradle/
.mvn/

# 日志
*.log
logs/

# 环境配置
.env
.env.local
.env.*.local

# 数据库
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 编译文件
*.o
*.a
*.so
*.dylib
*.exe
*.out

# 包管理器
package-lock.json
yarn.lock
pnpm-lock.yaml

# 测试覆盖率
coverage/
.nyc_output/

# 上传文件
uploads/
temp-uploads/