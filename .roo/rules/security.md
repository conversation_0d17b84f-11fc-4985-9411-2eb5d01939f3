# 安全规则
# 确保代码和项目安全性的详细规则

## 敏感信息保护
### 密码和密钥管理
- 绝不在代码中硬编码密码、API密钥或私钥
- 使用环境变量或安全的密钥管理服务
- 定期轮换密钥和证书
- 使用强密码策略（最小长度12位，包含大小写字母、数字和特殊字符）

### 配置文件安全
- 敏感配置必须加密存储
- 使用.gitignore排除敏感配置文件
- 提供配置模板而非真实配置
- 环境特定的配置分离存储

## 输入验证与防护
### SQL注入防护
- 使用参数化查询或ORM
- 验证所有用户输入
- 实施最小权限原则
- 限制数据库用户权限

### XSS防护
- 对所有用户输入进行HTML转义
- 使用内容安全策略(CSP)
- 验证和清理富文本内容
- 避免使用innerHTML等危险API

### 文件上传安全
- 验证文件类型和大小
- 使用白名单验证文件扩展名
- 存储上传文件到非执行目录
- 对上传文件进行病毒扫描

## 认证与授权
### 身份认证
- 使用强身份认证机制
- 实施多因素认证(MFA)
- 会话管理安全
- 定期更新认证令牌

### 权限控制
- 实施基于角色的访问控制(RBAC)
- 遵循最小权限原则
- 定期审查和更新权限
- 记录所有权限变更

## 数据保护
### 数据传输安全
- 使用HTTPS/TLS加密所有数据传输
- 验证SSL证书有效性
- 禁用不安全的协议和算法
- 实施证书固定(pinning)

### 数据存储安全
- 敏感数据必须加密存储
- 使用强加密算法(AES-256)
- 安全存储加密密钥
- 定期备份和测试恢复

## 日志与监控
### 安全日志
- 记录所有认证尝试
- 记录权限变更操作
- 监控异常访问模式
- 实施日志完整性保护

### 告警机制
- 实时安全事件告警
- 异常行为检测
- 定期安全审计
- 漏洞扫描和修复

## 依赖安全
### 第三方库管理
- 定期更新依赖库
- 使用漏洞扫描工具
- 评估第三方库安全性
- 建立依赖更新流程

### 供应链安全
- 验证依赖来源完整性
- 使用私有仓库缓存
- 实施依赖签名验证
- 监控供应链安全公告

## 移动端安全
### Android特定
- 启用ProGuard代码混淆
- 使用Android Keystore系统
- 实施根检测和调试检测
- 保护SharedPreferences数据

### 数据保护
- 避免在日志中输出敏感信息
- 使用安全的IPC机制
- 实施反调试保护
- 定期安全测试和渗透测试