# 基础规则
# 适用于所有项目的基础开发规范

## 代码风格
- 使用UTF-8编码
- 使用LF换行符（跨平台兼容）
- 缩进使用2个空格（除特定语言外）
- 最大行长度120字符
- 文件末尾保留空行

## 命名规范
### 文件命名
- 使用小写字母和连字符（kebab-case）
- 避免特殊字符和空格
- 使用描述性名称

### 变量和函数命名
- Java/Kotlin: 使用camelCase
- JavaScript/TypeScript: 使用camelCase
- Python: 使用snake_case
- CSS/SCSS: 使用kebab-case
- 数据库: 使用snake_case

### 类命名
- Java/Kotlin: 使用PascalCase
- JavaScript/TypeScript: 使用PascalCase
- Python: 使用PascalCase

## Git提交规范
- 使用中文或英文清晰描述
- 格式: `类型(范围): 描述`
- 类型包括: feat, fix, docs, style, refactor, test, chore
- 示例: `feat(登录): 添加指纹认证功能`

## 文件结构
- 按功能模块组织文件
- 每个目录包含README.md说明
- 测试文件与被测试文件同名，后缀.test或.spec
- 配置文件集中放在config/目录

## 文档规范
- 所有公共API必须包含文档注释
- 复杂业务逻辑需要注释说明
- 更新功能时同步更新文档

## 国际化
- 字符串资源外部化
- 支持多语言切换
- 日期时间格式本地化