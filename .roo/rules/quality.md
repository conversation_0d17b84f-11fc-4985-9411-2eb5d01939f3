# 代码质量规则
# 确保代码质量和可维护性的详细规则

## 代码复杂度
### 函数复杂度
- 单个函数不超过50行代码
- 嵌套层级不超过4层
- 圈复杂度不超过10
- 单个文件不超过500行

### 类设计
- 单一职责原则：每个类只负责一个功能
- 开闭原则：对扩展开放，对修改关闭
- 依赖倒置：依赖抽象而非具体实现
- 避免上帝对象

## 代码注释
### 必需注释
- 公共API必须有Javadoc/KDoc
- 复杂算法需要详细注释
- 业务规则需要解释
- 临时解决方案需标明TODO

### 注释规范
- 使用清晰简洁的语言
- 避免显而易见的注释
- 注释与代码保持同步
- 使用TODO/FIXME标签

## 错误处理
### 异常处理
- 不要捕获通用Exception
- 提供有意义的错误信息
- 记录异常上下文
- 避免空的catch块

### 输入验证
- 所有外部输入必须验证
- 使用防御性编程
- 提供默认值
- 防止注入攻击

## 测试要求
### 单元测试
- 所有公共方法必须有测试
- 测试覆盖率不低于80%
- 使用描述性的测试名称
- 遵循AAA模式（Arrange-Act-Assert）

### 集成测试
- 关键业务流程需要测试
- 测试真实场景
- 验证错误处理
- 检查性能指标

## 性能规范
### 内存使用
- 避免内存泄漏
- 及时释放资源
- 使用适当的数据结构
- 避免不必要的对象创建

### 响应时间
- UI操作响应时间<100ms
- API响应时间<500ms
- 数据库查询优化
- 异步处理耗时操作

## 日志规范
### 日志级别
- DEBUG: 调试信息，开发环境使用
- INFO: 重要业务流程
- WARN: 潜在问题
- ERROR: 错误和异常

### 日志内容
- 包含时间戳和线程信息
- 提供足够的上下文
- 避免记录敏感信息
- 使用结构化日志格式