# .roo 配置系统文档

## 概述
.roo目录是一个全面的配置系统，为项目提供标准化的开发规范、代码规则、提示模板、代理配置、工具配置和环境管理。

## 目录结构

```
.roo/
├── README.md                 # 本文档
├── rules/                    # 代码规则和最佳实践
│   ├── base.md              # 基础规则
│   ├── quality.md           # 代码质量规则
│   └── security.md          # 安全规则
├── prompts/                  # AI提示模板
│   ├── code-review.md       # 代码审查模板
│   ├── api-design.md        # API设计模板
│   └── refactoring.md       # 代码重构模板
├── agents/                   # 专家代理配置
│   ├── android-expert.md    # Android开发专家
│   └── backend-expert.md    # 后端开发专家
├── tools/                    # 工具配置
│   ├── lint-config.json     # 代码检查配置
│   └── build-config.json    # 构建系统配置
├── templates/                # 代码模板
│   ├── gitignore-template.txt      # .gitignore模板
│   └── android-class-template.kt   # Android类模板
└── environments/             # 环境配置
    ├── development.json     # 开发环境
    ├── staging.json         # 测试环境
    └── production.json      # 生产环境
```

## 使用指南

### 1. 规则应用
将规则文件中的内容应用到项目中：
- **基础规则** - 适用于所有项目的通用规范
- **代码质量规则** - 确保代码质量和可维护性
- **安全规则** - 保护代码和项目安全

### 2. 提示模板使用
使用提示模板来标准化AI交互：
- **代码审查** - 系统化的代码质量检查
- **API设计** - RESTful API设计规范
- **代码重构** - 重构策略和步骤

### 3. 代理配置
根据项目类型选择合适的专家代理：
- **Android专家** - Android开发最佳实践
- **后端专家** - 后端开发技术栈指导

### 4. 工具配置
配置开发工具以保持一致性：
- **代码检查** - 支持多种语言的lint配置
- **构建系统** - 跨平台构建配置

### 5. 模板使用
使用提供的模板快速创建新文件：
- **gitignore** - 通用忽略规则模板
- **Android类** - 标准化的Android组件模板

### 6. 环境配置
管理不同环境的配置：
- **开发环境** - 本地开发配置
- **测试环境** - 集成测试配置
- **生产环境** - 生产部署配置

## 环境变量使用
生产环境配置使用环境变量占位符，需要在部署时设置：
- `${DB_HOST}` - 数据库主机
- `${DB_NAME}` - 数据库名称
- `${DB_USER}` - 数据库用户
- `${DB_PASSWORD}` - 数据库密码
- `${JWT_SECRET}` - JWT密钥
- `${CORS_ORIGINS}` - CORS源列表

## 扩展指南

### 添加新规则
1. 在`rules/`目录创建新规则文件
2. 遵循现有格式和结构
3. 更新本README文档

### 添加新模板
1. 在`templates/`目录创建新模板文件
2. 使用描述性文件名
3. 添加使用说明

### 添加新环境
1. 复制现有环境配置
2. 根据需求调整配置
3. 更新环境变量列表

## 最佳实践

1. **定期更新** - 定期审查和更新配置
2. **团队共识** - 确保团队成员理解并遵循配置
3. **版本控制** - 将.roo目录纳入版本控制
4. **文档同步** - 保持文档与实际配置同步
5. **渐进式采用** - 逐步引入配置，避免一次性改变过多

## 技术支持

如有问题或建议，请：
1. 检查现有配置是否满足需求
2. 创建Issue描述具体需求
3. 提交Pull Request进行改进