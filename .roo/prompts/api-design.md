# API设计提示模板
# 用于指导API设计的标准模板

## 设计目标
设计一个RESTful API，满足以下要求：

## 设计原则
1. **RESTful原则** - 遵循REST架构风格
2. **一致性** - 保持端点和响应格式的一致性
3. **可扩展性** - 支持未来的功能扩展
4. **安全性** - 实施适当的安全措施
5. **性能** - 优化响应时间和资源使用

## API规范
### 端点设计
- 使用名词复数形式表示资源
- 使用HTTP动词表示操作
- 保持URL简洁明了
- 版本控制（如/api/v1/）

### 请求/响应格式
- 使用JSON作为数据交换格式
- 统一的响应结构
- 适当的HTTP状态码
- 错误信息标准化

### 分页和过滤
- 支持分页查询
- 提供排序选项
- 实现字段过滤
- 搜索功能

## 安全考虑
- 身份认证（JWT Token）
- 请求频率限制
- 输入验证和清理
- HTTPS强制使用

## 示例模板

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应
```json
{
  "success": true,
  "data": [],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

## 文档要求
- 使用OpenAPI/Swagger规范
- 提供详细的端点说明
- 包含请求/响应示例
- 错误码说明完整