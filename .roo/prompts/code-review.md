# 代码审查提示模板
# 用于指导AI进行代码审查的标准模板

## 审查范围
请对以下代码进行全面的质量审查：

## 审查重点
1. **代码质量** - 检查代码是否符合项目规范
2. **安全性** - 识别潜在的安全漏洞
3. **性能** - 发现性能优化机会
4. **可维护性** - 评估代码的可读性和可维护性
5. **测试覆盖率** - 检查是否包含充分的测试

## 审查清单
- [ ] 代码遵循项目的命名规范
- [ ] 函数长度和复杂度在合理范围内
- [ ] 错误处理机制完善
- [ ] 没有硬编码的敏感信息
- [ ] 输入验证充分
- [ ] 日志记录适当
- [ ] 单元测试覆盖主要逻辑
- [ ] 代码注释清晰且必要

## 输出格式
请按以下格式提供审查结果：

### 发现的问题
1. **问题类型**: 具体描述
   - 位置: 文件名:行号
   - 建议: 改进建议
   - 严重程度: 高/中/低

### 正面评价
列出代码中做得好的方面

### 改进建议
提供具体的代码改进建议