# 代码重构提示模板
# 用于指导代码重构的标准模板

## 重构目标
对现有代码进行重构，提高其：

1. **可读性** - 使代码更易于理解
2. **可维护性** - 降低修改成本
3. **可扩展性** - 支持未来需求变化
4. **性能** - 提升执行效率
5. **测试性** - 便于编写单元测试

## 重构策略

### 1. 代码坏味道识别
- [ ] 过长函数
- [ ] 过大类
- [ ] 重复代码
- [ ] 过长参数列表
- [ ] 发散式变化
- [ ] 霰弹式修改
- [ ] 依恋情节
- [ ] 数据泥团
- [ ] 基本类型偏执
- [ ] switch语句
- [ ] 平行继承体系
- [ ] 冗余类/函数
- [ ] 过度设计

### 2. 重构手法

#### 函数级别
- **提炼函数** (Extract Method)
- **内联函数** (Inline Method)
- **提炼变量** (Extract Variable)
- **内联变量** (Inline Variable)
- **改变函数声明** (Change Function Declaration)
- **封装变量** (Encapsulate Variable)
- **变量改名** (Rename Variable)
- **引入参数对象** (Introduce Parameter Object)

#### 类级别
- **提炼类** (Extract Class)
- **内联类** (Inline Class)
- **隐藏委托关系** (Hide Delegate)
- **移除中间人** (Remove Middle Man)
- **引入外部函数** (Introduce Foreign Method)
- **引入本地扩展** (Introduce Local Extension)

#### 数据级别
- **封装记录** (Encapsulate Record)
- **封装集合** (Encapsulate Collection)
- **以对象取代基本类型** (Replace Primitive with Object)
- **以查询取代临时变量** (Replace Temp with Query)

#### 条件逻辑
- **分解条件表达式** (Decompose Conditional)
- **合并条件表达式** (Consolidate Conditional Expression)
- **合并重复的条件片段** (Consolidate Duplicate Conditional Fragments)
- **移除控制标记** (Remove Control Flag)
- **以多态取代条件表达式** (Replace Conditional with Polymorphism)

## 重构步骤

### 第一阶段：理解现有代码
1. 阅读并理解现有代码逻辑
2. 识别代码坏味道
3. 标记重构优先级

### 第二阶段：小步重构
1. 每次只做一个小改动
2. 运行测试确保功能不变
3. 逐步应用重构手法

### 第三阶段：验证结果
1. 验证所有测试通过
2. 检查性能是否改善
3. 评估代码质量提升

## 注意事项
- 重构前确保有充分的测试覆盖
- 小步快跑，频繁提交
- 保持功能行为不变
- 关注代码可读性
- 考虑性能影响
- 团队协作沟通