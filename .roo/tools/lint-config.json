{"name": "项目代码检查配置", "description": "跨语言代码质量检查工具配置", "version": "1.0.0", "android": {"tool": "ktlint", "config": {"android": true, "code_style": "android_studio", "max_line_length": 120, "indent_size": 4, "insert_final_newline": true, "trim_trailing_whitespace": true, "disabled_rules": ["import-ordering", "final-newline"]}, "file_patterns": ["*.kt", "*.kts"]}, "java": {"tool": "checkstyle", "config": {"config_file": "google_checks.xml", "properties": {"max_line_length": 120, "indent_size": 2}}, "file_patterns": ["*.java"]}, "javascript": {"tool": "eslint", "config": {"extends": ["eslint:recommended", "prettier"], "env": {"browser": true, "node": true, "es2022": true}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error", "max-len": ["error", 120]}}, "file_patterns": ["*.js", "*.mjs", "*.ts"]}, "typescript": {"tool": "tslint", "config": {"extends": ["tslint:recommended", "tslint-config-prettier"], "rules": {"max-line-length": [true, 120], "indent": [true, "spaces", 2], "quotemark": [true, "single"]}}, "file_patterns": ["*.ts", "*.tsx"]}, "python": {"tool": "pylint", "config": {"max-line-length": 120, "indent-size": 4, "disable": ["C0111", "R0903"], "output-format": "json"}, "file_patterns": ["*.py"]}, "css": {"tool": "stylelint", "config": {"extends": ["stylelint-config-standard", "stylelint-config-prettier"], "rules": {"indentation": 2, "max-line-length": 120, "string-quotes": "single"}}, "file_patterns": ["*.css", "*.scss", "*.sass"]}, "markdown": {"tool": "markdownlint", "config": {"default": true, "MD013": {"line_length": 120}, "MD024": false, "MD033": false}, "file_patterns": ["*.md"]}, "yaml": {"tool": "yam<PERSON><PERSON>", "config": {"extends": "default", "rules": {"line-length": {"max": 120}, "truthy": {"allowed-values": ["true", "false"]}}}, "file_patterns": ["*.yml", "*.yaml"]}, "json": {"tool": "jsonlint", "config": {"indent": 2, "compact": false}, "file_patterns": ["*.json"]}}