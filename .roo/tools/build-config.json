{"name": "项目构建配置", "description": "跨平台构建系统配置", "version": "1.0.0", "android": {"build_tool": "gradle", "gradle_version": "8.5", "android_gradle_plugin": "8.2.2", "build_types": {"debug": {"debuggable": true, "minifyEnabled": false, "shrinkResources": false, "proguardFiles": []}, "release": {"debuggable": false, "minifyEnabled": true, "shrinkResources": true, "proguardFiles": ["proguard-rules.pro"], "signingConfig": "release"}}, "dependencies": {"kotlin_version": "1.9.22", "compile_sdk": 34, "min_sdk": 21, "target_sdk": 34}}, "nodejs": {"package_manager": "npm", "node_version": "18.x", "scripts": {"dev": "nodemon src/index.js", "build": "webpack --mode production", "test": "jest", "lint": "eslint src/**/*.js", "format": "prettier --write src/**/*.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "dev_dependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "webpack": "^5.89.0"}}, "python": {"build_tool": "poetry", "python_version": "3.11", "dependencies": {"fastapi": "^0.109.0", "uvicorn": "^0.27.0", "sqlalchemy": "^2.0.25", "alembic": "^1.13.1", "psycopg2-binary": "^2.9.9", "redis": "^5.0.1", "pydantic": "^2.5.3"}, "dev_dependencies": {"pytest": "^7.4.4", "pytest-asyncio": "^0.23.3", "black": "^23.12.1", "flake8": "^7.0.0", "mypy": "^1.8.0"}}, "docker": {"base_images": {"android": "openjdk:11-jdk-slim", "nodejs": "node:18-alpine", "python": "python:3.11-slim"}, "multi_stage": true, "build_args": {"BUILD_ENV": "production"}, "labels": {"maintainer": "<EMAIL>", "version": "1.0.0"}}, "ci_cd": {"platform": "github-actions", "triggers": {"push": ["main", "develop"], "pull_request": ["main", "develop"]}, "workflows": {"test": {"runs_on": "ubuntu-latest", "steps": ["checkout", "setup", "lint", "test", "coverage"]}, "build": {"runs_on": "ubuntu-latest", "steps": ["checkout", "setup", "build", "docker", "deploy"]}}}}